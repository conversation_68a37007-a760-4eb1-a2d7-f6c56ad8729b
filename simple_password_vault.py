#!/usr/bin/env python3
"""
🔐 Simple Password Vault
A clean, simple password manager with modern design
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import json
import os
import secrets
import string
import hashlib

class SimplePasswordVault:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔐 Simple Password Vault")
        self.root.geometry("900x600")
        self.root.configure(bg='#1a1a2e')
        
        # Color scheme
        self.colors = {
            'bg': '#1a1a2e',
            'card': '#16213e',
            'accent': '#e94560',
            'success': '#00d4aa',
            'warning': '#ffbe0b',
            'danger': '#fb5607',
            'text': '#ffffff',
            'text_dim': '#a0a0a0'
        }
        
        # Data
        self.data_file = "simple_vault_data.json"
        self.passwords = {}
        self.master_password = None
        
        self.setup_styles()
        self.show_login()
    
    def setup_styles(self):
        """Setup modern ttk styles."""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Button style
        style.configure('Modern.TButton',
                       background=self.colors['accent'],
                       foreground='white',
                       font=('Arial', 10, 'bold'),
                       borderwidth=0)
        
        style.map('Modern.TButton',
                 background=[('active', '#f38ba8')])
        
        # Entry style
        style.configure('Modern.TEntry',
                       fieldbackground=self.colors['card'],
                       foreground=self.colors['text'],
                       borderwidth=1)
    
    def show_login(self):
        """Show login screen."""
        self.clear_window()
        
        # Main frame
        main_frame = tk.Frame(self.root, bg=self.colors['bg'])
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Login card
        login_frame = tk.Frame(main_frame, bg=self.colors['card'], padx=40, pady=40)
        login_frame.place(relx=0.5, rely=0.5, anchor='center')
        
        # Title
        title_label = tk.Label(login_frame, text="🔐 Password Vault", 
                              font=('Arial', 24, 'bold'),
                              bg=self.colors['card'], fg=self.colors['text'])
        title_label.pack(pady=(0, 30))
        
        # Master password
        tk.Label(login_frame, text="Master Password:", 
                font=('Arial', 12, 'bold'),
                bg=self.colors['card'], fg=self.colors['text']).pack(anchor='w')
        
        self.master_entry = tk.Entry(login_frame, show="*", width=30, font=('Arial', 12),
                                    bg=self.colors['bg'], fg=self.colors['text'],
                                    insertbackground=self.colors['text'])
        self.master_entry.pack(pady=(5, 20), ipady=8)
        
        # Buttons
        button_frame = tk.Frame(login_frame, bg=self.colors['card'])
        button_frame.pack(fill=tk.X)
        
        login_btn = tk.Button(button_frame, text="🔓 Unlock Vault", 
                             command=self.login,
                             bg=self.colors['accent'], fg='white',
                             font=('Arial', 11, 'bold'), relief='flat',
                             padx=20, pady=10)
        login_btn.pack(fill=tk.X, pady=(0, 10))
        
        create_btn = tk.Button(button_frame, text="✨ Create New Vault", 
                              command=self.create_vault,
                              bg=self.colors['card'], fg=self.colors['text'],
                              font=('Arial', 11, 'bold'), relief='flat',
                              padx=20, pady=10)
        create_btn.pack(fill=tk.X)
        
        # Bind Enter key
        self.master_entry.bind('<Return>', lambda e: self.login())
        self.master_entry.focus()
    
    def hash_password(self, password):
        """Hash password."""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def login(self):
        """Handle login."""
        password = self.master_entry.get()
        if not password:
            messagebox.showwarning("Warning", "Please enter your master password!")
            return
        
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r') as f:
                    data = json.load(f)
                
                if data.get('master_hash') == self.hash_password(password):
                    self.passwords = data.get('passwords', {})
                    self.master_password = password
                    self.show_main_interface()
                    messagebox.showinfo("Success", "Vault unlocked successfully!")
                else:
                    messagebox.showerror("Error", "Invalid master password!")
            except Exception as e:
                messagebox.showerror("Error", f"Error loading vault: {e}")
        else:
            messagebox.showwarning("Warning", "No vault found. Create a new one!")
    
    def create_vault(self):
        """Create new vault."""
        password = self.master_entry.get()
        if not password:
            messagebox.showwarning("Warning", "Please enter a master password!")
            return
        
        if len(password) < 6:
            messagebox.showwarning("Warning", "Master password must be at least 6 characters!")
            return
        
        self.passwords = {}
        self.master_password = password
        self.save_data()
        messagebox.showinfo("Success", "Vault created successfully!")
        self.show_main_interface()
    
    def save_data(self):
        """Save data to file."""
        data = {
            'master_hash': self.hash_password(self.master_password),
            'passwords': self.passwords
        }
        
        with open(self.data_file, 'w') as f:
            json.dump(data, f, indent=2)
    
    def show_main_interface(self):
        """Show main interface."""
        self.clear_window()
        
        # Main frame
        main_frame = tk.Frame(self.root, bg=self.colors['bg'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Header
        header_frame = tk.Frame(main_frame, bg=self.colors['card'], height=80)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        header_frame.pack_propagate(False)
        
        # Title
        title_label = tk.Label(header_frame, text="🔐 Password Vault", 
                              font=('Arial', 18, 'bold'),
                              bg=self.colors['card'], fg=self.colors['text'])
        title_label.pack(side=tk.LEFT, padx=20, pady=20)
        
        # Count
        count_label = tk.Label(header_frame, text=f"{len(self.passwords)} passwords", 
                              font=('Arial', 12),
                              bg=self.colors['card'], fg=self.colors['text_dim'])
        count_label.pack(side=tk.LEFT, padx=(0, 20), pady=20)
        
        # Buttons
        btn_frame = tk.Frame(header_frame, bg=self.colors['card'])
        btn_frame.pack(side=tk.RIGHT, padx=20, pady=15)
        
        tk.Button(btn_frame, text="➕ Add", command=self.add_password,
                 bg=self.colors['accent'], fg='white', font=('Arial', 10, 'bold'),
                 relief='flat', padx=15, pady=5).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(btn_frame, text="🎲 Generate", command=self.generate_password,
                 bg=self.colors['success'], fg='white', font=('Arial', 10, 'bold'),
                 relief='flat', padx=15, pady=5).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(btn_frame, text="🚪 Logout", command=self.logout,
                 bg=self.colors['danger'], fg='white', font=('Arial', 10, 'bold'),
                 relief='flat', padx=15, pady=5).pack(side=tk.RIGHT, padx=5)
        
        # Search
        search_frame = tk.Frame(main_frame, bg=self.colors['card'], height=50)
        search_frame.pack(fill=tk.X, pady=(0, 20))
        search_frame.pack_propagate(False)
        
        tk.Label(search_frame, text="🔍 Search:", font=('Arial', 12),
                bg=self.colors['card'], fg=self.colors['text']).pack(side=tk.LEFT, padx=20, pady=15)
        
        self.search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=self.search_var, width=30,
                               bg=self.colors['bg'], fg=self.colors['text'],
                               font=('Arial', 11), insertbackground=self.colors['text'])
        search_entry.pack(side=tk.LEFT, padx=(10, 20), pady=15, ipady=5)
        self.search_var.trace_add('write', self.filter_passwords)
        
        # Passwords list
        list_frame = tk.Frame(main_frame, bg=self.colors['card'])
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # Treeview
        columns = ('Site', 'Username', 'Strength')
        self.tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        self.tree.heading('Site', text='Site/Service')
        self.tree.heading('Username', text='Username')
        self.tree.heading('Strength', text='Strength')
        
        self.tree.column('Site', width=250)
        self.tree.column('Username', width=200)
        self.tree.column('Strength', width=100)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=20, pady=20)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=20, padx=(0, 20))
        
        # Context menu
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="👁️ View", command=self.view_selected)
        self.context_menu.add_command(label="📋 Copy Password", command=self.copy_selected)
        self.context_menu.add_command(label="✏️ Edit", command=self.edit_selected)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="🗑️ Delete", command=self.delete_selected)
        
        self.tree.bind("<Button-3>", self.show_context_menu)
        self.tree.bind("<Double-1>", lambda e: self.view_selected())
        
        self.refresh_list()
    
    def clear_window(self):
        """Clear all widgets."""
        for widget in self.root.winfo_children():
            widget.destroy()
    
    def refresh_list(self):
        """Refresh passwords list."""
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # Add passwords
        search_term = self.search_var.get().lower() if hasattr(self, 'search_var') else ""
        
        for site, data in self.passwords.items():
            if search_term and search_term not in site.lower() and search_term not in data.get('username', '').lower():
                continue
            
            strength = self.get_password_strength(data.get('password', ''))
            self.tree.insert('', tk.END, values=(site, data.get('username', 'N/A'), strength))
    
    def get_password_strength(self, password):
        """Get password strength."""
        if len(password) < 6:
            return "Weak"
        
        score = 0
        if len(password) >= 8:
            score += 1
        if any(c.isupper() for c in password):
            score += 1
        if any(c.islower() for c in password):
            score += 1
        if any(c.isdigit() for c in password):
            score += 1
        if any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            score += 1
        
        return "Strong" if score >= 4 else "Medium" if score >= 2 else "Weak"
    
    def filter_passwords(self, *args):
        """Filter passwords."""
        self.refresh_list()
    
    def show_context_menu(self, event):
        """Show context menu."""
        if self.tree.selection():
            self.context_menu.post(event.x_root, event.y_root)
    
    def get_selected_site(self):
        """Get selected site name."""
        selection = self.tree.selection()
        if selection:
            return self.tree.item(selection[0])['values'][0]
        return None

    def add_password(self):
        """Add new password."""
        self.show_password_dialog("Add New Password")

    def show_password_dialog(self, title, site="", username="", password=""):
        """Show password dialog."""
        dialog = tk.Toplevel(self.root)
        dialog.title(title)
        dialog.geometry("400x300")
        dialog.configure(bg=self.colors['card'])
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # Center dialog
        dialog.geometry("+%d+%d" % (
            self.root.winfo_rootx() + 250,
            self.root.winfo_rooty() + 150
        ))

        # Form
        form_frame = tk.Frame(dialog, bg=self.colors['card'], padx=30, pady=30)
        form_frame.pack(fill=tk.BOTH, expand=True)

        # Site
        tk.Label(form_frame, text="Site/Service:", font=('Arial', 11, 'bold'),
                bg=self.colors['card'], fg=self.colors['text']).pack(anchor='w')

        site_entry = tk.Entry(form_frame, width=35, font=('Arial', 11),
                             bg=self.colors['bg'], fg=self.colors['text'],
                             insertbackground=self.colors['text'])
        site_entry.pack(fill=tk.X, pady=(5, 15), ipady=5)
        if site:
            site_entry.insert(0, site)

        # Username
        tk.Label(form_frame, text="Username/Email:", font=('Arial', 11, 'bold'),
                bg=self.colors['card'], fg=self.colors['text']).pack(anchor='w')

        username_entry = tk.Entry(form_frame, width=35, font=('Arial', 11),
                                 bg=self.colors['bg'], fg=self.colors['text'],
                                 insertbackground=self.colors['text'])
        username_entry.pack(fill=tk.X, pady=(5, 15), ipady=5)
        if username:
            username_entry.insert(0, username)

        # Password
        tk.Label(form_frame, text="Password:", font=('Arial', 11, 'bold'),
                bg=self.colors['card'], fg=self.colors['text']).pack(anchor='w')

        password_frame = tk.Frame(form_frame, bg=self.colors['card'])
        password_frame.pack(fill=tk.X, pady=(5, 20))

        password_entry = tk.Entry(password_frame, show="*", width=30, font=('Arial', 11),
                                 bg=self.colors['bg'], fg=self.colors['text'],
                                 insertbackground=self.colors['text'])
        password_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, ipady=5)
        if password:
            password_entry.insert(0, password)

        # Generate button
        def generate():
            new_password = self.generate_secure_password()
            password_entry.delete(0, tk.END)
            password_entry.insert(0, new_password)

        tk.Button(password_frame, text="🎲", command=generate,
                 bg=self.colors['success'], fg='white', font=('Arial', 10, 'bold'),
                 relief='flat', width=3).pack(side=tk.RIGHT, padx=(10, 0), ipady=5)

        # Buttons
        button_frame = tk.Frame(form_frame, bg=self.colors['card'])
        button_frame.pack(fill=tk.X, pady=(10, 0))

        def save():
            site_name = site_entry.get().strip()
            user_name = username_entry.get().strip()
            pass_word = password_entry.get()

            if not site_name:
                messagebox.showwarning("Warning", "Please enter a site name!")
                return

            if not pass_word:
                messagebox.showwarning("Warning", "Please enter a password!")
                return

            self.passwords[site_name] = {
                'username': user_name,
                'password': pass_word
            }

            self.save_data()
            self.refresh_list()
            messagebox.showinfo("Success", f"Password for {site_name} saved!")
            dialog.destroy()

        tk.Button(button_frame, text="💾 Save", command=save,
                 bg=self.colors['accent'], fg='white', font=('Arial', 11, 'bold'),
                 relief='flat', padx=20, pady=8).pack(side=tk.RIGHT, padx=(10, 0))

        tk.Button(button_frame, text="❌ Cancel", command=dialog.destroy,
                 bg=self.colors['bg'], fg=self.colors['text'], font=('Arial', 11, 'bold'),
                 relief='flat', padx=20, pady=8).pack(side=tk.RIGHT)

        site_entry.focus()

    def generate_password(self):
        """Generate password dialog."""
        password = self.generate_secure_password()

        dialog = tk.Toplevel(self.root)
        dialog.title("Generated Password")
        dialog.geometry("400x200")
        dialog.configure(bg=self.colors['card'])
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # Center dialog
        dialog.geometry("+%d+%d" % (
            self.root.winfo_rootx() + 250,
            self.root.winfo_rooty() + 200
        ))

        frame = tk.Frame(dialog, bg=self.colors['card'], padx=30, pady=30)
        frame.pack(fill=tk.BOTH, expand=True)

        tk.Label(frame, text="Generated Password:", font=('Arial', 12, 'bold'),
                bg=self.colors['card'], fg=self.colors['text']).pack(anchor='w')

        password_text = tk.Text(frame, height=3, width=40, font=('Courier', 11, 'bold'),
                               bg=self.colors['bg'], fg=self.colors['text'])
        password_text.pack(fill=tk.X, pady=(10, 20))
        password_text.insert(1.0, password)
        password_text.configure(state='readonly')

        def copy_password():
            self.root.clipboard_clear()
            self.root.clipboard_append(password)
            messagebox.showinfo("Success", "Password copied to clipboard!")

        button_frame = tk.Frame(frame, bg=self.colors['card'])
        button_frame.pack(fill=tk.X)

        tk.Button(button_frame, text="📋 Copy", command=copy_password,
                 bg=self.colors['success'], fg='white', font=('Arial', 11, 'bold'),
                 relief='flat', padx=20, pady=8).pack(side=tk.LEFT)

        tk.Button(button_frame, text="❌ Close", command=dialog.destroy,
                 bg=self.colors['bg'], fg=self.colors['text'], font=('Arial', 11, 'bold'),
                 relief='flat', padx=20, pady=8).pack(side=tk.RIGHT)

    def generate_secure_password(self, length=16):
        """Generate secure password."""
        chars = string.ascii_letters + string.digits + "!@#$%^&*()_+-="
        return ''.join(secrets.choice(chars) for _ in range(length))

    def view_selected(self):
        """View selected password."""
        site = self.get_selected_site()
        if not site or site not in self.passwords:
            messagebox.showwarning("Warning", "Please select a password to view!")
            return

        data = self.passwords[site]

        dialog = tk.Toplevel(self.root)
        dialog.title(f"View Password - {site}")
        dialog.geometry("350x250")
        dialog.configure(bg=self.colors['card'])
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # Center dialog
        dialog.geometry("+%d+%d" % (
            self.root.winfo_rootx() + 275,
            self.root.winfo_rooty() + 175
        ))

        frame = tk.Frame(dialog, bg=self.colors['card'], padx=30, pady=30)
        frame.pack(fill=tk.BOTH, expand=True)

        # Site
        tk.Label(frame, text="Site:", font=('Arial', 11, 'bold'),
                bg=self.colors['card'], fg=self.colors['text_dim']).pack(anchor='w')
        tk.Label(frame, text=site, font=('Arial', 12),
                bg=self.colors['card'], fg=self.colors['text']).pack(anchor='w', pady=(0, 10))

        # Username
        tk.Label(frame, text="Username:", font=('Arial', 11, 'bold'),
                bg=self.colors['card'], fg=self.colors['text_dim']).pack(anchor='w')
        tk.Label(frame, text=data.get('username', 'N/A'), font=('Arial', 12),
                bg=self.colors['card'], fg=self.colors['text']).pack(anchor='w', pady=(0, 10))

        # Password
        tk.Label(frame, text="Password:", font=('Arial', 11, 'bold'),
                bg=self.colors['card'], fg=self.colors['text_dim']).pack(anchor='w')

        password_frame = tk.Frame(frame, bg=self.colors['card'])
        password_frame.pack(fill=tk.X, pady=(0, 20))

        password_label = tk.Label(password_frame, text="••••••••••••", font=('Arial', 12),
                                 bg=self.colors['card'], fg=self.colors['text'])
        password_label.pack(side=tk.LEFT)

        show_var = tk.BooleanVar()
        def toggle_password():
            if show_var.get():
                password_label.configure(text=data.get('password', ''))
                show_btn.configure(text="🙈")
            else:
                password_label.configure(text="••••••••••••")
                show_btn.configure(text="👁️")

        show_btn = tk.Button(password_frame, text="👁️", command=toggle_password,
                            bg=self.colors['accent'], fg='white', font=('Arial', 10),
                            relief='flat', width=3)
        show_btn.pack(side=tk.RIGHT)

        # Buttons
        button_frame = tk.Frame(frame, bg=self.colors['card'])
        button_frame.pack(fill=tk.X)

        def copy_password():
            self.root.clipboard_clear()
            self.root.clipboard_append(data.get('password', ''))
            messagebox.showinfo("Success", f"Password for {site} copied!")

        tk.Button(button_frame, text="📋 Copy", command=copy_password,
                 bg=self.colors['success'], fg='white', font=('Arial', 11, 'bold'),
                 relief='flat', padx=15, pady=8).pack(side=tk.LEFT)

        tk.Button(button_frame, text="❌ Close", command=dialog.destroy,
                 bg=self.colors['bg'], fg=self.colors['text'], font=('Arial', 11, 'bold'),
                 relief='flat', padx=15, pady=8).pack(side=tk.RIGHT)

    def copy_selected(self):
        """Copy selected password."""
        site = self.get_selected_site()
        if site and site in self.passwords:
            password = self.passwords[site].get('password', '')
            self.root.clipboard_clear()
            self.root.clipboard_append(password)
            messagebox.showinfo("Success", f"Password for {site} copied!")
        else:
            messagebox.showwarning("Warning", "Please select a password to copy!")

    def edit_selected(self):
        """Edit selected password."""
        site = self.get_selected_site()
        if site and site in self.passwords:
            data = self.passwords[site]
            self.show_password_dialog(f"Edit Password - {site}",
                                     site, data.get('username', ''), data.get('password', ''))
        else:
            messagebox.showwarning("Warning", "Please select a password to edit!")

    def delete_selected(self):
        """Delete selected password."""
        site = self.get_selected_site()
        if not site or site not in self.passwords:
            messagebox.showwarning("Warning", "Please select a password to delete!")
            return

        result = messagebox.askyesno("Confirm Delete",
                                   f"Are you sure you want to delete the password for {site}?")

        if result:
            del self.passwords[site]
            self.save_data()
            self.refresh_list()
            messagebox.showinfo("Success", f"Password for {site} deleted!")

    def logout(self):
        """Logout."""
        self.passwords = {}
        self.master_password = None
        self.show_login()
        messagebox.showinfo("Success", "Logged out successfully!")

    def run(self):
        """Run the application."""
        # Center window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (900 // 2)
        y = (self.root.winfo_screenheight() // 2) - (600 // 2)
        self.root.geometry(f"900x600+{x}+{y}")

        self.root.mainloop()

if __name__ == "__main__":
    app = SimplePasswordVault()
    app.run()
