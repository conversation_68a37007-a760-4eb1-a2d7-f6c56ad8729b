#!/usr/bin/env python3
"""
Simple Graphic Adventure Game
A lightweight GUI-based adventure game using only tkinter (no external dependencies).
"""

import tkinter as tk
from tkinter import messagebox, simpledialog
import random

class Player:
    """Player character with stats and inventory management."""
    
    def __init__(self, name: str = "Adventurer"):
        self.name = name
        self.health = 100
        self.max_health = 100
        self.attack = 10
        self.defense = 5
        self.gold = 0
        self.experience = 0
        self.level = 1
        self.inventory = []
    
    def add_item(self, item: str):
        self.inventory.append(item)
        return f"✅ Added {item} to inventory!"
    
    def has_item(self, item: str) -> bool:
        return item in self.inventory
    
    def heal(self, amount: int):
        old_health = self.health
        self.health = min(self.max_health, self.health + amount)
        healed = self.health - old_health
        return f"💚 Healed for {healed} HP!" if healed > 0 else "💚 Already at full health!"
    
    def take_damage(self, damage: int):
        actual_damage = max(1, damage - self.defense)
        self.health -= actual_damage
        return f"💥 You take {actual_damage} damage! Health: {self.health}/{self.max_health}"
    
    def gain_experience(self, xp: int):
        self.experience += xp
        message = f"⭐ Gained {xp} XP!"
        
        if self.experience >= self.level * 100:
            self.level += 1
            self.max_health += 20
            self.health = self.max_health
            self.attack += 3
            self.defense += 2
            message += f"\n🎉 LEVEL UP! You are now level {self.level}!"
        
        return message

class Enemy:
    """Enemy class for combat encounters."""
    
    def __init__(self, name: str, health: int, attack: int, defense: int, xp_reward: int, gold_reward: int):
        self.name = name
        self.health = health
        self.max_health = health
        self.attack = attack
        self.defense = defense
        self.xp_reward = xp_reward
        self.gold_reward = gold_reward
    
    def take_damage(self, damage: int) -> bool:
        actual_damage = max(1, damage - self.defense)
        self.health -= actual_damage
        return self.health <= 0

class SimpleGraphicAdventure:
    """Main game class with simple GUI interface."""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎮 Simple Graphic Adventure Game")
        self.root.geometry("900x600")
        self.root.configure(bg='#2c3e50')
        
        # Game state
        self.player = None
        self.current_enemy = None
        
        # GUI elements
        self.setup_gui()
        self.start_game()
    
    def setup_gui(self):
        """Setup the main GUI layout."""
        # Main frame
        self.main_frame = tk.Frame(self.root, bg='#2c3e50')
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Top frame for player stats
        self.stats_frame = tk.Frame(self.main_frame, bg='#34495e', relief=tk.RAISED, bd=2)
        self.stats_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Player stats labels
        self.name_label = tk.Label(self.stats_frame, text="Player: ", font=('Arial', 12, 'bold'), 
                                  bg='#34495e', fg='white')
        self.name_label.grid(row=0, column=0, padx=10, pady=5, sticky='w')
        
        self.health_label = tk.Label(self.stats_frame, text="❤️ Health: 100/100", font=('Arial', 10), 
                                    bg='#34495e', fg='#e74c3c')
        self.health_label.grid(row=0, column=1, padx=10, pady=5)
        
        self.level_label = tk.Label(self.stats_frame, text="⭐ Level: 1", font=('Arial', 10), 
                                   bg='#34495e', fg='#f39c12')
        self.level_label.grid(row=0, column=2, padx=10, pady=5)
        
        self.gold_label = tk.Label(self.stats_frame, text="💰 Gold: 0", font=('Arial', 10), 
                                  bg='#34495e', fg='#f1c40f')
        self.gold_label.grid(row=0, column=3, padx=10, pady=5)
        
        # Middle frame for game content
        self.content_frame = tk.Frame(self.main_frame, bg='#2c3e50')
        self.content_frame.pack(fill=tk.BOTH, expand=True)
        
        # Left side - Scene display
        self.scene_frame = tk.Frame(self.content_frame, bg='#34495e', relief=tk.SUNKEN, bd=2)
        self.scene_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        self.scene_label = tk.Label(self.scene_frame, bg='#34495e', text="🎮 Adventure Game", 
                                   font=('Arial', 20, 'bold'), fg='white', wraplength=400)
        self.scene_label.pack(expand=True, padx=20, pady=20)
        
        # Right side - Controls and text
        self.control_frame = tk.Frame(self.content_frame, bg='#2c3e50', width=400)
        self.control_frame.pack(side=tk.RIGHT, fill=tk.Y)
        self.control_frame.pack_propagate(False)
        
        # Story text area
        self.story_frame = tk.Frame(self.control_frame, bg='#34495e', relief=tk.SUNKEN, bd=2)
        self.story_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        self.story_text = tk.Text(self.story_frame, wrap=tk.WORD, font=('Arial', 10), 
                                 bg='#ecf0f1', fg='#2c3e50', height=15, state=tk.DISABLED)
        self.story_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Scrollbar for story text
        scrollbar = tk.Scrollbar(self.story_frame, command=self.story_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.story_text.config(yscrollcommand=scrollbar.set)
        
        # Buttons frame
        self.buttons_frame = tk.Frame(self.control_frame, bg='#2c3e50')
        self.buttons_frame.pack(fill=tk.X)
        
        # Action buttons (will be created dynamically)
        self.action_buttons = []
        
        # Bottom frame for inventory and special actions
        self.bottom_frame = tk.Frame(self.main_frame, bg='#34495e', relief=tk.RAISED, bd=2)
        self.bottom_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.inventory_label = tk.Label(self.bottom_frame, text="🎒 Inventory: Empty", 
                                       font=('Arial', 10), bg='#34495e', fg='white')
        self.inventory_label.pack(side=tk.LEFT, padx=10, pady=5)
        
        # Special action buttons
        self.stats_button = tk.Button(self.bottom_frame, text="📊 Stats", command=self.show_stats,
                                     bg='#3498db', fg='white', font=('Arial', 9, 'bold'))
        self.stats_button.pack(side=tk.RIGHT, padx=5, pady=5)
        
        self.inventory_button = tk.Button(self.bottom_frame, text="🎒 Inventory", command=self.show_inventory,
                                         bg='#9b59b6', fg='white', font=('Arial', 9, 'bold'))
        self.inventory_button.pack(side=tk.RIGHT, padx=5, pady=5)
    
    def update_scene(self, scene_text: str, bg_color: str = '#34495e'):
        """Update the scene display."""
        self.scene_label.configure(text=scene_text, bg=bg_color)
        self.scene_frame.configure(bg=bg_color)
    
    def add_story_text(self, text: str):
        """Add text to the story display."""
        self.story_text.config(state=tk.NORMAL)
        self.story_text.insert(tk.END, text + "\n\n")
        self.story_text.see(tk.END)
        self.story_text.config(state=tk.DISABLED)
    
    def clear_story_text(self):
        """Clear the story text area."""
        self.story_text.config(state=tk.NORMAL)
        self.story_text.delete(1.0, tk.END)
        self.story_text.config(state=tk.DISABLED)
    
    def update_stats_display(self):
        """Update the stats display."""
        if self.player:
            self.name_label.config(text=f"Player: {self.player.name}")
            self.health_label.config(text=f"❤️ Health: {self.player.health}/{self.player.max_health}")
            self.level_label.config(text=f"⭐ Level: {self.player.level}")
            self.gold_label.config(text=f"💰 Gold: {self.player.gold}")
            
            # Update inventory display
            if self.player.inventory:
                inv_text = f"🎒 Inventory: {', '.join(self.player.inventory[:3])}"
                if len(self.player.inventory) > 3:
                    inv_text += f" (+{len(self.player.inventory) - 3} more)"
            else:
                inv_text = "🎒 Inventory: Empty"
            self.inventory_label.config(text=inv_text)
    
    def clear_action_buttons(self):
        """Clear all action buttons."""
        for button in self.action_buttons:
            button.destroy()
        self.action_buttons = []
    
    def create_action_buttons(self, actions):
        """Create action buttons from a list of (text, command) tuples."""
        self.clear_action_buttons()
        
        colors = ['#e74c3c', '#3498db', '#2ecc71', '#f39c12', '#9b59b6']
        
        for i, (text, command) in enumerate(actions):
            color = colors[i % len(colors)]
            button = tk.Button(self.buttons_frame, text=text, command=command,
                              bg=color, fg='white', font=('Arial', 10, 'bold'),
                              width=25, height=2)
            button.pack(pady=2, fill=tk.X)
            self.action_buttons.append(button)
    
    def start_game(self):
        """Start the game with character creation."""
        self.clear_story_text()
        self.update_scene("🌟 Welcome to the\nGraphic Adventure Game! 🌟", '#3498db')
        self.add_story_text("🌟 Welcome to the Graphic Adventure Game! 🌟")
        self.add_story_text("You are about to embark on an epic journey...")
        
        # Get player name
        name = simpledialog.askstring("Character Creation", "What is your name, brave adventurer?")
        if not name:
            name = "Adventurer"
        
        self.player = Player(name)
        self.update_stats_display()
        
        self.add_story_text(f"Welcome, {self.player.name}! Your adventure begins now...")
        self.show_crossroads()
    
    def show_crossroads(self):
        """Display the main crossroads."""
        self.update_scene("🗺️ The Crossroads\n\nThree paths await\nyour choice...", '#3498db')
        
        self.add_story_text("🏰 You find yourself standing before three mysterious paths...")
        self.add_story_text("Each path leads to different adventures and challenges.")
        
        actions = [
            ("🌲 Forest Path", self.forest_path),
            ("🏔️ Mountain Path", self.mountain_path),
            ("🏜️ Desert Path", self.desert_path),
            ("📊 Check Stats", self.show_stats)
        ]
        
        self.create_action_buttons(actions)
    
    def forest_path(self):
        """Forest adventure path."""
        self.update_scene("🌲 Enchanted Forest\n\nMagical creatures\ndart between\nancient trees", '#27ae60')
        
        self.add_story_text("🌲 You venture into the Enchanted Forest...")
        self.add_story_text("Sunlight filters through ancient trees, and magical creatures dart between shadows.")
        
        # Simple encounter
        encounter = random.choice(["fairy", "treasure", "wolf"])
        
        if encounter == "fairy":
            self.add_story_text("✨ A glowing fairy approaches with a warm smile!")
            self.add_story_text("'I can grant you a gift, traveler!'")
            
            actions = [
                ("💚 Ask for Healing", lambda: self.fairy_gift("healing")),
                ("⚔️ Ask for Strength", lambda: self.fairy_gift("strength")),
                ("🏠 Return to Crossroads", self.show_crossroads)
            ]
        elif encounter == "treasure":
            self.add_story_text("💎 You discover a treasure chest!")
            gold_found = random.randint(20, 50)
            self.player.gold += gold_found
            self.add_story_text(f"💰 You found {gold_found} gold!")
            self.update_stats_display()
            
            actions = [
                ("🏠 Return to Crossroads", self.show_crossroads),
                ("🌲 Explore More", self.forest_path)
            ]
        else:  # wolf
            self.add_story_text("🐺 A fierce wolf blocks your path!")
            self.current_enemy = Enemy("Forest Wolf", 40, 12, 2, 25, 15)
            
            actions = [
                ("⚔️ Fight", self.start_combat),
                ("🏃 Run Away", self.show_crossroads)
            ]
        
        self.create_action_buttons(actions)
    
    def fairy_gift(self, gift_type: str):
        """Handle fairy gift."""
        if gift_type == "healing":
            message = self.player.heal(50)
            self.add_story_text("✨ The fairy heals you with magic!")
            self.add_story_text(message)
        else:  # strength
            self.player.attack += 5
            self.add_story_text("✨ The fairy blesses you with strength!")
            self.add_story_text("⚔️ Your attack power increased!")
        
        self.update_stats_display()
        
        actions = [
            ("🏠 Return to Crossroads", self.show_crossroads),
            ("🌲 Continue in Forest", self.forest_path)
        ]
        self.create_action_buttons(actions)
    
    def mountain_path(self):
        """Mountain adventure path."""
        self.update_scene("🏔️ Mountain Path\n\nSteep rocky trails\ndisappear into\nthe mist", '#95a5a6')
        
        self.add_story_text("🏔️ You climb the treacherous mountain path...")
        self.add_story_text("The air grows thin and cold as you ascend.")
        
        encounter = random.choice(["dragon", "treasure", "troll"])
        
        if encounter == "dragon":
            self.add_story_text("🐉 You discover a dragon's cave!")
            self.add_story_text("The mighty beast sleeps on a pile of gold.")
            
            actions = [
                ("⚔️ Challenge Dragon", self.dragon_fight),
                ("🤫 Sneak Past", self.sneak_dragon),
                ("🏃 Retreat", self.show_crossroads)
            ]
        elif encounter == "treasure":
            self.add_story_text("💎 You find an ancient treasure hoard!")
            gold_found = random.randint(40, 80)
            self.player.gold += gold_found
            exp_gained = 30
            exp_msg = self.player.gain_experience(exp_gained)
            self.add_story_text(f"💰 You found {gold_found} gold!")
            self.add_story_text(exp_msg)
            self.update_stats_display()
            
            actions = [
                ("🏠 Return to Crossroads", self.show_crossroads),
                ("🏔️ Continue Climbing", self.mountain_path)
            ]
        else:  # troll
            self.add_story_text("👹 A massive troll blocks the path!")
            self.add_story_text("'Pay toll or fight!' he demands.")
            
            actions = [
                ("💰 Pay 30 Gold", self.pay_troll),
                ("⚔️ Fight Troll", self.troll_fight),
                ("🏃 Go Back", self.show_crossroads)
            ]
        
        self.create_action_buttons(actions)
    
    def desert_path(self):
        """Desert adventure path."""
        self.update_scene("🏜️ Desert Sands\n\nEndless dunes\nstretch under\nthe burning sun", '#f39c12')
        
        self.add_story_text("🏜️ You venture into the vast desert...")
        self.add_story_text("The sun beats down mercilessly.")
        
        # Simple desert encounter
        if random.random() < 0.5:
            self.add_story_text("🌴 You find an oasis!")
            heal_msg = self.player.heal(30)
            self.add_story_text(heal_msg)
            self.player.add_item("Water Flask")
            self.add_story_text("💧 You fill your water flask.")
        else:
            self.add_story_text("🏴‍☠️ Desert bandits attack!")
            self.current_enemy = Enemy("Desert Bandit", 35, 10, 1, 20, 20)
            
            actions = [
                ("⚔️ Fight", self.start_combat),
                ("🏃 Run Away", self.show_crossroads)
            ]
            self.create_action_buttons(actions)
            return
        
        self.update_stats_display()
        
        actions = [
            ("🏠 Return to Crossroads", self.show_crossroads),
            ("🏜️ Continue in Desert", self.desert_path)
        ]
        self.create_action_buttons(actions)
    
    def start_combat(self):
        """Start combat with current enemy."""
        if not self.current_enemy:
            return
        
        self.update_scene(f"⚔️ COMBAT!\n\n{self.player.name}\nvs\n{self.current_enemy.name}", '#e74c3c')
        self.add_story_text(f"⚔️ COMBAT: {self.player.name} vs {self.current_enemy.name}!")
        self.add_story_text(f"Enemy: {self.current_enemy.health} HP")
        
        self.combat_turn()
    
    def combat_turn(self):
        """Handle combat turn."""
        if not self.current_enemy or self.current_enemy.health <= 0:
            self.combat_victory()
            return
        
        if self.player.health <= 0:
            self.game_over()
            return
        
        actions = [
            ("⚔️ Attack", self.player_attack),
            ("🛡️ Defend", self.player_defend),
            ("🏃 Try to Run", self.try_run_combat)
        ]
        
        self.create_action_buttons(actions)
    
    def player_attack(self):
        """Player attacks enemy."""
        damage = random.randint(self.player.attack - 2, self.player.attack + 2)
        if self.current_enemy.take_damage(damage):
            self.add_story_text(f"💥 You deal {damage} damage and defeat the enemy!")
            self.combat_victory()
        else:
            self.add_story_text(f"💥 You deal {damage} damage! Enemy health: {self.current_enemy.health}")
            self.enemy_turn()
    
    def player_defend(self):
        """Player defends."""
        self.add_story_text("🛡️ You brace for the enemy's attack!")
        self.enemy_turn(defend=True)
    
    def enemy_turn(self, defend=False):
        """Enemy's turn to attack."""
        damage = random.randint(self.current_enemy.attack - 1, self.current_enemy.attack + 1)
        if defend:
            damage = max(1, damage - 3)  # Reduce damage when defending
        
        damage_msg = self.player.take_damage(damage)
        self.add_story_text(f"🐉 {self.current_enemy.name} attacks!")
        self.add_story_text(damage_msg)
        
        self.update_stats_display()
        
        if self.player.health <= 0:
            self.game_over()
        else:
            self.combat_turn()
    
    def combat_victory(self):
        """Handle combat victory."""
        self.add_story_text(f"🎉 Victory! You defeated {self.current_enemy.name}!")
        
        exp_msg = self.player.gain_experience(self.current_enemy.xp_reward)
        self.player.gold += self.current_enemy.gold_reward
        
        self.add_story_text(exp_msg)
        self.add_story_text(f"💰 You found {self.current_enemy.gold_reward} gold!")
        
        self.current_enemy = None
        self.update_stats_display()
        
        # Check for victory condition
        if self.player.level >= 5:
            self.victory_ending()
            return
        
        actions = [
            ("🏠 Return to Crossroads", self.show_crossroads)
        ]
        self.create_action_buttons(actions)
    
    def show_stats(self):
        """Show detailed player statistics."""
        if self.player:
            stats = f"""📊 {self.player.name}'s Stats:
            
❤️ Health: {self.player.health}/{self.player.max_health}
⚔️ Attack: {self.player.attack}
🛡️ Defense: {self.player.defense}
💰 Gold: {self.player.gold}
⭐ Level: {self.player.level}
🎯 Experience: {self.player.experience}

🎒 Inventory: {', '.join(self.player.inventory) if self.player.inventory else 'Empty'}"""
            
            messagebox.showinfo("Player Statistics", stats)
    
    def show_inventory(self):
        """Show inventory details."""
        if self.player and self.player.inventory:
            inv_text = "🎒 Your Inventory:\n\n" + "\n".join([f"• {item}" for item in self.player.inventory])
        else:
            inv_text = "🎒 Your inventory is empty."
        
        messagebox.showinfo("Inventory", inv_text)
    
    def game_over(self):
        """Handle game over."""
        self.update_scene("💀 GAME OVER\n\nYour adventure\nends here...", '#8b0000')
        self.add_story_text("💀 GAME OVER")
        
        result = messagebox.askyesno("Game Over", 
                                   f"💀 {self.player.name} has fallen!\n\nWould you like to start a new adventure?")
        
        if result:
            self.start_game()
        else:
            self.root.quit()
    
    def victory_ending(self):
        """Handle victory."""
        self.update_scene("🏆 VICTORY!\n\nYou are now a\nlegendary\nadventurer!", '#2ecc71')
        self.add_story_text("🏆 VICTORY! You have become a legendary adventurer!")
        
        result = messagebox.askyesno("Victory!", 
                                   f"🏆 Congratulations {self.player.name}!\n\nYou've reached level {self.player.level}!\n\nStart a new adventure?")
        
        if result:
            self.start_game()
        else:
            self.root.quit()
    
    # Simplified encounter methods
    def sneak_dragon(self):
        if random.random() < 0.4:
            gold = random.randint(30, 60)
            self.player.gold += gold
            self.add_story_text(f"🤫 You successfully sneak past and grab {gold} gold!")
        else:
            self.add_story_text("👁️ The dragon spots you!")
            self.dragon_fight()
            return
        
        self.update_stats_display()
        actions = [("🏠 Return to Crossroads", self.show_crossroads)]
        self.create_action_buttons(actions)
    
    def dragon_fight(self):
        self.current_enemy = Enemy("Ancient Dragon", 80, 20, 5, 80, 100)
        self.start_combat()
    
    def pay_troll(self):
        if self.player.gold >= 30:
            self.player.gold -= 30
            self.add_story_text("💰 You pay the troll and pass safely.")
            self.update_stats_display()
            actions = [("🏠 Return to Crossroads", self.show_crossroads)]
        else:
            self.add_story_text("💸 You don't have enough gold! The troll attacks!")
            self.troll_fight()
            return
        
        self.create_action_buttons(actions)
    
    def troll_fight(self):
        self.current_enemy = Enemy("Mountain Troll", 60, 15, 4, 50, 35)
        self.start_combat()
    
    def try_run_combat(self):
        if random.random() < 0.6:
            self.add_story_text("💨 You successfully ran away!")
            self.current_enemy = None
            self.show_crossroads()
        else:
            self.add_story_text("❌ Couldn't escape!")
            self.enemy_turn()
    
    def run(self):
        """Start the GUI main loop."""
        self.root.mainloop()

def main():
    """Main function to run the game."""
    try:
        game = SimpleGraphicAdventure()
        game.run()
    except Exception as e:
        print(f"Error running the game: {e}")

if __name__ == "__main__":
    main()
