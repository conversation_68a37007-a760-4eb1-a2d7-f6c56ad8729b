#!/usr/bin/env python3
"""
Graphic Adventure Game
A GUI-based adventure game using tkinter with images, buttons, and visual elements.
"""

import tkinter as tk
from tkinter import messagebox, simpledialog
import random
from typing import Dict, List, Optional

# Try to import PIL for enhanced graphics, but make it optional
try:
    from PIL import Image, ImageTk, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

class Player:
    """Player character with stats and inventory management."""

    def __init__(self, name: str = "Adventurer"):
        self.name = name
        self.health = 100
        self.max_health = 100
        self.attack = 10
        self.defense = 5
        self.gold = 0
        self.experience = 0
        self.level = 1
        self.inventory = []
        self.current_location = "crossroads"

    def add_item(self, item: str):
        """Add item to inventory."""
        self.inventory.append(item)
        return f"✅ Added {item} to inventory!"

    def has_item(self, item: str) -> bool:
        """Check if player has specific item."""
        return item in self.inventory

    def heal(self, amount: int):
        """Heal the player."""
        old_health = self.health
        self.health = min(self.max_health, self.health + amount)
        healed = self.health - old_health
        return f"💚 Healed for {healed} HP!" if healed > 0 else "💚 Already at full health!"

    def take_damage(self, damage: int):
        """Apply damage to player."""
        actual_damage = max(1, damage - self.defense)
        self.health -= actual_damage
        return f"💥 You take {actual_damage} damage! Health: {self.health}/{self.max_health}"

    def gain_experience(self, xp: int):
        """Add experience and check for level up."""
        self.experience += xp
        message = f"⭐ Gained {xp} XP!"

        # Level up every 100 XP
        if self.experience >= self.level * 100:
            self.level += 1
            self.max_health += 20
            self.health = self.max_health
            self.attack += 3
            self.defense += 2
            message += f"\n🎉 LEVEL UP! You are now level {self.level}!"

        return message

class Enemy:
    """Enemy class for combat encounters."""

    def __init__(self, name: str, health: int, attack: int, defense: int, xp_reward: int, gold_reward: int):
        self.name = name
        self.health = health
        self.max_health = health
        self.attack = attack
        self.defense = defense
        self.xp_reward = xp_reward
        self.gold_reward = gold_reward

    def take_damage(self, damage: int) -> bool:
        """Apply damage to enemy and return True if defeated."""
        actual_damage = max(1, damage - self.defense)
        self.health -= actual_damage
        return self.health <= 0

class GraphicAdventureGame:
    """Main game class with GUI interface."""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎮 Graphic Adventure Game")
        self.root.geometry("1000x700")
        self.root.configure(bg='#2c3e50')

        # Game state
        self.player = None
        self.current_enemy = None
        self.game_images = {}

        # GUI elements
        self.setup_gui()
        self.load_images()

        # Start the game
        self.start_game()

    def setup_gui(self):
        """Setup the main GUI layout."""
        # Main frame
        self.main_frame = tk.Frame(self.root, bg='#2c3e50')
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Top frame for player stats
        self.stats_frame = tk.Frame(self.main_frame, bg='#34495e', relief=tk.RAISED, bd=2)
        self.stats_frame.pack(fill=tk.X, pady=(0, 10))

        # Player stats labels
        self.name_label = tk.Label(self.stats_frame, text="Player: ", font=('Arial', 12, 'bold'),
                                  bg='#34495e', fg='white')
        self.name_label.grid(row=0, column=0, padx=10, pady=5, sticky='w')

        self.health_label = tk.Label(self.stats_frame, text="❤️ Health: 100/100", font=('Arial', 10),
                                    bg='#34495e', fg='#e74c3c')
        self.health_label.grid(row=0, column=1, padx=10, pady=5)

        self.level_label = tk.Label(self.stats_frame, text="⭐ Level: 1", font=('Arial', 10),
                                   bg='#34495e', fg='#f39c12')
        self.level_label.grid(row=0, column=2, padx=10, pady=5)

        self.gold_label = tk.Label(self.stats_frame, text="💰 Gold: 0", font=('Arial', 10),
                                  bg='#34495e', fg='#f1c40f')
        self.gold_label.grid(row=0, column=3, padx=10, pady=5)

        # Middle frame for game content
        self.content_frame = tk.Frame(self.main_frame, bg='#2c3e50')
        self.content_frame.pack(fill=tk.BOTH, expand=True)

        # Left side - Image display
        self.image_frame = tk.Frame(self.content_frame, bg='#34495e', relief=tk.SUNKEN, bd=2)
        self.image_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        self.image_label = tk.Label(self.image_frame, bg='#34495e', text="🎮 Adventure Game",
                                   font=('Arial', 24, 'bold'), fg='white')
        self.image_label.pack(expand=True)

        # Right side - Controls and text
        self.control_frame = tk.Frame(self.content_frame, bg='#2c3e50', width=400)
        self.control_frame.pack(side=tk.RIGHT, fill=tk.Y)
        self.control_frame.pack_propagate(False)

        # Story text area
        self.story_frame = tk.Frame(self.control_frame, bg='#34495e', relief=tk.SUNKEN, bd=2)
        self.story_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        self.story_text = tk.Text(self.story_frame, wrap=tk.WORD, font=('Arial', 11),
                                 bg='#ecf0f1', fg='#2c3e50', height=15, state=tk.DISABLED)
        self.story_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Scrollbar for story text
        scrollbar = tk.Scrollbar(self.story_frame, command=self.story_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.story_text.config(yscrollcommand=scrollbar.set)

        # Buttons frame
        self.buttons_frame = tk.Frame(self.control_frame, bg='#2c3e50')
        self.buttons_frame.pack(fill=tk.X)

        # Action buttons (will be created dynamically)
        self.action_buttons = []

        # Bottom frame for inventory and special actions
        self.bottom_frame = tk.Frame(self.main_frame, bg='#34495e', relief=tk.RAISED, bd=2)
        self.bottom_frame.pack(fill=tk.X, pady=(10, 0))

        self.inventory_label = tk.Label(self.bottom_frame, text="🎒 Inventory: Empty",
                                       font=('Arial', 10), bg='#34495e', fg='white')
        self.inventory_label.pack(side=tk.LEFT, padx=10, pady=5)

        # Special action buttons
        self.stats_button = tk.Button(self.bottom_frame, text="📊 Stats", command=self.show_stats,
                                     bg='#3498db', fg='white', font=('Arial', 9, 'bold'))
        self.stats_button.pack(side=tk.RIGHT, padx=5, pady=5)

        self.inventory_button = tk.Button(self.bottom_frame, text="🎒 Inventory", command=self.show_inventory,
                                         bg='#9b59b6', fg='white', font=('Arial', 9, 'bold'))
        self.inventory_button.pack(side=tk.RIGHT, padx=5, pady=5)

    def load_images(self):
        """Load or create placeholder images for different locations."""
        # Create simple colored rectangles as placeholder images
        self.create_placeholder_images()

    def create_placeholder_images(self):
        """Create colored placeholder images for different locations."""
        if PIL_AVAILABLE:
            # Create images for different locations
            locations = {
                'crossroads': ('#3498db', '🗺️ Crossroads'),
                'forest': ('#27ae60', '🌲 Enchanted Forest'),
                'mountain': ('#95a5a6', '🏔️ Mountain Path'),
                'desert': ('#f39c12', '🏜️ Desert Sands'),
                'combat': ('#e74c3c', '⚔️ Combat!'),
                'victory': ('#2ecc71', '🏆 Victory!'),
                'treasure': ('#f1c40f', '💎 Treasure!')
            }

            for location, (color, text) in locations.items():
                # Create a 400x300 image
                img = Image.new('RGB', (400, 300), color)
                draw = ImageDraw.Draw(img)

                # Try to use a font, fall back to default if not available
                try:
                    font = ImageFont.truetype("arial.ttf", 24)
                except:
                    font = ImageFont.load_default()

                # Get text size and center it
                bbox = draw.textbbox((0, 0), text, font=font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]
                x = (400 - text_width) // 2
                y = (300 - text_height) // 2

                draw.text((x, y), text, fill='white', font=font)

                # Convert to PhotoImage for tkinter
                self.game_images[location] = ImageTk.PhotoImage(img)
        else:
            # If PIL is not available, use text-only display
            self.game_images = {}

    def update_image(self, location: str):
        """Update the displayed image based on current location."""
        if location in self.game_images:
            self.image_label.configure(image=self.game_images[location], text="")
        else:
            # Fallback to text display
            location_texts = {
                'crossroads': '🗺️ Crossroads',
                'forest': '🌲 Enchanted Forest',
                'mountain': '🏔️ Mountain Path',
                'desert': '🏜️ Desert Sands',
                'combat': '⚔️ Combat!',
                'victory': '🏆 Victory!',
                'treasure': '💎 Treasure!'
            }
            text = location_texts.get(location, '🎮 Adventure Game')
            self.image_label.configure(image="", text=text)

    def add_story_text(self, text: str):
        """Add text to the story display."""
        self.story_text.config(state=tk.NORMAL)
        self.story_text.insert(tk.END, text + "\n\n")
        self.story_text.see(tk.END)
        self.story_text.config(state=tk.DISABLED)

    def clear_story_text(self):
        """Clear the story text area."""
        self.story_text.config(state=tk.NORMAL)
        self.story_text.delete(1.0, tk.END)
        self.story_text.config(state=tk.DISABLED)

    def update_stats_display(self):
        """Update the stats display."""
        if self.player:
            self.name_label.config(text=f"Player: {self.player.name}")
            self.health_label.config(text=f"❤️ Health: {self.player.health}/{self.player.max_health}")
            self.level_label.config(text=f"⭐ Level: {self.player.level}")
            self.gold_label.config(text=f"💰 Gold: {self.player.gold}")

            # Update inventory display
            if self.player.inventory:
                inv_text = f"🎒 Inventory: {', '.join(self.player.inventory[:3])}"
                if len(self.player.inventory) > 3:
                    inv_text += f" (+{len(self.player.inventory) - 3} more)"
            else:
                inv_text = "🎒 Inventory: Empty"
            self.inventory_label.config(text=inv_text)

    def clear_action_buttons(self):
        """Clear all action buttons."""
        for button in self.action_buttons:
            button.destroy()
        self.action_buttons = []

    def create_action_buttons(self, actions: List[tuple]):
        """Create action buttons from a list of (text, command) tuples."""
        self.clear_action_buttons()

        colors = ['#e74c3c', '#3498db', '#2ecc71', '#f39c12', '#9b59b6']

        for i, (text, command) in enumerate(actions):
            color = colors[i % len(colors)]
            button = tk.Button(self.buttons_frame, text=text, command=command,
                              bg=color, fg='white', font=('Arial', 11, 'bold'),
                              width=20, height=2)
            button.pack(pady=2, fill=tk.X)
            self.action_buttons.append(button)

    def start_game(self):
        """Start the game with character creation."""
        self.clear_story_text()
        self.add_story_text("🌟 Welcome to the Graphic Adventure Game! 🌟")
        self.add_story_text("You are about to embark on an epic journey...")

        # Get player name
        name = simpledialog.askstring("Character Creation", "What is your name, brave adventurer?")
        if not name:
            name = "Adventurer"

        self.player = Player(name)
        self.update_stats_display()

        self.add_story_text(f"Welcome, {self.player.name}! Your adventure begins now...")
        self.show_crossroads()

    def show_crossroads(self):
        """Display the main crossroads."""
        self.player.current_location = "crossroads"
        self.update_image("crossroads")

        self.add_story_text("🏰 You find yourself standing before three mysterious paths...")
        self.add_story_text("Each path leads to different adventures and challenges.")
        self.add_story_text("🗺️ Three paths stretch before you:")

        actions = [
            ("🌲 Forest Path", self.forest_path),
            ("🏔️ Mountain Path", self.mountain_path),
            ("🏜️ Desert Path", self.desert_path)
        ]

        self.create_action_buttons(actions)

    def show_stats(self):
        """Show detailed player statistics."""
        if self.player:
            stats = f"""📊 {self.player.name}'s Detailed Stats:

❤️ Health: {self.player.health}/{self.player.max_health}
⚔️ Attack: {self.player.attack}
🛡️ Defense: {self.player.defense}
💰 Gold: {self.player.gold}
⭐ Level: {self.player.level}
🎯 Experience: {self.player.experience}
🎒 Inventory Items: {len(self.player.inventory)}

Items: {', '.join(self.player.inventory) if self.player.inventory else 'None'}"""

            messagebox.showinfo("Player Statistics", stats)

    def show_inventory(self):
        """Show detailed inventory."""
        if self.player:
            if self.player.inventory:
                inv_text = "🎒 Your Inventory:\n\n" + "\n".join([f"• {item}" for item in self.player.inventory])
            else:
                inv_text = "🎒 Your inventory is empty."

            messagebox.showinfo("Inventory", inv_text)

    def run(self):
        """Start the GUI main loop."""
        self.root.mainloop()

    def forest_path(self):
        """Forest adventure path."""
        self.player.current_location = "forest"
        self.update_image("forest")

        self.add_story_text("🌲 You venture into the Enchanted Forest...")
        self.add_story_text("Sunlight filters through ancient trees, and magical creatures dart between shadows.")

        # Random encounter
        encounter_type = random.choice(["fairy", "wolf", "treasure"])

        if encounter_type == "fairy":
            self.fairy_encounter()
        elif encounter_type == "wolf":
            self.wolf_encounter()
        else:
            self.treasure_encounter()

    def fairy_encounter(self):
        """Encounter with a friendly fairy."""
        self.add_story_text("✨ A glowing fairy approaches you with a warm smile.")
        self.add_story_text("'Greetings, traveler! I can offer you a gift.'")

        actions = [
            ("💚 Ask for Healing", lambda: self.fairy_gift("healing")),
            ("⚔️ Ask for Weapon", lambda: self.fairy_gift("weapon")),
            ("🧠 Ask for Wisdom", lambda: self.fairy_gift("wisdom")),
            ("🏠 Return to Crossroads", self.show_crossroads)
        ]

        self.create_action_buttons(actions)

    def fairy_gift(self, gift_type: str):
        """Handle fairy gift selection."""
        if gift_type == "healing":
            message = self.player.heal(50)
            self.add_story_text("✨ The fairy's magic restores your vitality!")
            self.add_story_text(message)
        elif gift_type == "weapon":
            self.player.add_item("Fairy Blade")
            self.player.attack += 5
            self.add_story_text("✨ The fairy grants you a magical blade that glows with inner light!")
            self.add_story_text("⚔️ Your attack power increased!")
        elif gift_type == "wisdom":
            message = self.player.gain_experience(50)
            self.add_story_text("✨ The fairy shares ancient knowledge with you!")
            self.add_story_text(message)

        self.update_stats_display()

        actions = [
            ("🏠 Return to Crossroads", self.show_crossroads),
            ("🌲 Explore Forest More", self.forest_path)
        ]
        self.create_action_buttons(actions)

    def wolf_encounter(self):
        """Combat encounter with a wolf."""
        self.add_story_text("🐺 A fierce wolf blocks your path, growling menacingly!")
        self.current_enemy = Enemy("Forest Wolf", 40, 12, 2, 25, 15)

        actions = [
            ("⚔️ Fight the Wolf", self.start_combat),
            ("🏃 Try to Run", self.try_run_away),
            ("🥩 Offer Food (if you have any)", self.try_tame_wolf)
        ]

        self.create_action_buttons(actions)

    def treasure_encounter(self):
        """Finding a treasure chest."""
        self.update_image("treasure")
        self.add_story_text("💎 You discover a mysterious treasure chest hidden behind a tree!")
        self.add_story_text("The chest is locked with an intricate mechanism.")

        actions = [
            ("💪 Force it Open", lambda: self.open_chest("force")),
            ("🔍 Examine Carefully", lambda: self.open_chest("examine")),
            ("🚶 Leave it Alone", self.show_crossroads)
        ]

        if self.player.has_item("Lockpick"):
            actions.insert(0, ("🔓 Use Lockpick", lambda: self.open_chest("lockpick")))

        self.create_action_buttons(actions)

    def open_chest(self, method: str):
        """Handle treasure chest opening."""
        success = False

        if method == "lockpick":
            self.add_story_text("🔓 You skillfully use your lockpick to open the chest!")
            success = True
        elif method == "force":
            if random.random() < 0.6:  # 60% success rate
                self.add_story_text("💪 You manage to break the lock!")
                success = True
            else:
                self.add_story_text("💥 The chest explodes with a trap! You take damage.")
                damage_msg = self.player.take_damage(15)
                self.add_story_text(damage_msg)
                success = False
        elif method == "examine":
            self.add_story_text("🔍 You carefully examine the lock and find a hidden mechanism!")
            self.add_story_text(self.player.gain_experience(20))
            success = True

        if success:
            self.give_treasure()

        self.update_stats_display()

        actions = [
            ("🏠 Return to Crossroads", self.show_crossroads),
            ("🌲 Continue in Forest", self.forest_path)
        ]
        self.create_action_buttons(actions)

    def give_treasure(self):
        """Give random treasure to player."""
        treasures = [
            ("Gold Coins", lambda: setattr(self.player, 'gold', self.player.gold + random.randint(20, 50))),
            ("Health Potion", lambda: self.player.add_item("Health Potion")),
            ("Magic Ring", lambda: self.give_magic_ring()),
            ("Ancient Map", lambda: self.player.add_item("Ancient Map"))
        ]

        treasure_name, treasure_effect = random.choice(treasures)
        treasure_effect()

        if treasure_name == "Gold Coins":
            self.add_story_text(f"💰 You found {self.player.gold - (self.player.gold - random.randint(20, 50))} gold coins!")
        elif treasure_name == "Magic Ring":
            self.add_story_text("💍 The magic ring increases your defense!")
        else:
            self.add_story_text(f"✨ You found: {treasure_name}!")

    def give_magic_ring(self):
        """Give magic ring and increase defense."""
        self.player.add_item("Magic Ring")
        self.player.defense += 3

    def mountain_path(self):
        """Mountain adventure path."""
        self.player.current_location = "mountain"
        self.update_image("mountain")

        self.add_story_text("🏔️ You begin the treacherous climb up the mountain...")
        self.add_story_text("The air grows thin and cold as you ascend the rocky path.")

        encounter_type = random.choice(["dragon", "troll", "shrine"])

        if encounter_type == "dragon":
            self.dragon_encounter()
        elif encounter_type == "troll":
            self.troll_encounter()
        else:
            self.shrine_encounter()

    def dragon_encounter(self):
        """Epic dragon encounter."""
        self.add_story_text("🐉 You discover a cave with ominous smoke rising from within...")
        self.add_story_text("Peering inside, you see a MASSIVE dragon sleeping on a pile of gold!")

        actions = [
            ("🤫 Try to Sneak Past", self.sneak_past_dragon),
            ("⚔️ Challenge the Dragon", self.challenge_dragon),
            ("🏃 Retreat Safely", self.show_crossroads)
        ]

        self.create_action_buttons(actions)

    def sneak_past_dragon(self):
        """Attempt to sneak past the dragon."""
        if self.player.has_item("Stealth Cloak") or random.random() < 0.3:
            self.add_story_text("🤫 You successfully sneak past and grab some treasure!")
            gold_found = random.randint(50, 100)
            self.player.gold += gold_found
            self.add_story_text(f"💰 You found {gold_found} gold!")
            self.add_story_text(self.player.gain_experience(40))
        else:
            self.add_story_text("👁️ The dragon's eye opens! You've been spotted!")
            self.dragon_fight()
            return

        self.update_stats_display()
        actions = [
            ("🏠 Return to Crossroads", self.show_crossroads),
            ("🏔️ Continue on Mountain", self.mountain_path)
        ]
        self.create_action_buttons(actions)

    def challenge_dragon(self):
        """Challenge the dragon to combat."""
        self.add_story_text("⚔️ You boldly challenge the dragon to combat!")
        self.dragon_fight()

    def dragon_fight(self):
        """Fight the dragon boss."""
        self.add_story_text("🔥 The dragon awakens with a mighty roar!")
        self.current_enemy = Enemy("Ancient Dragon", 120, 25, 8, 100, 200)
        self.start_combat()

    def start_combat(self):
        """Start combat with current enemy."""
        if not self.current_enemy:
            return

        self.update_image("combat")
        self.add_story_text(f"⚔️ COMBAT: {self.player.name} vs {self.current_enemy.name}!")
        self.add_story_text(f"🐉 {self.current_enemy.name}: {self.current_enemy.health} HP")

        self.combat_turn()

    def combat_turn(self):
        """Handle a single combat turn."""
        if not self.current_enemy or self.current_enemy.health <= 0:
            self.combat_victory()
            return

        if self.player.health <= 0:
            self.combat_defeat()
            return

        actions = [
            ("⚔️ Attack", self.player_attack),
            ("🛡️ Defend", self.player_defend),
            ("🧪 Use Item", self.use_item_in_combat),
            ("🏃 Try to Run", self.try_run_from_combat)
        ]

        self.create_action_buttons(actions)

    def player_attack(self):
        """Player attacks the enemy."""
        damage = random.randint(self.player.attack - 2, self.player.attack + 2)
        if self.current_enemy.take_damage(damage):
            self.add_story_text(f"💥 You deal {damage} damage!")
            self.combat_victory()
            return
        else:
            self.add_story_text(f"💥 You deal {damage} damage! Enemy health: {self.current_enemy.health}")

        # Enemy turn
        self.enemy_attack()

    def enemy_attack(self):
        """Enemy attacks the player."""
        enemy_damage = random.randint(self.current_enemy.attack - 1, self.current_enemy.attack + 1)
        damage_msg = self.player.take_damage(enemy_damage)
        self.add_story_text(f"🐉 {self.current_enemy.name} attacks!")
        self.add_story_text(damage_msg)

        self.update_stats_display()

        if self.player.health <= 0:
            self.combat_defeat()
        else:
            self.combat_turn()

    def combat_victory(self):
        """Handle combat victory."""
        self.add_story_text(f"🎉 You defeated {self.current_enemy.name}!")

        # Rewards
        exp_msg = self.player.gain_experience(self.current_enemy.xp_reward)
        self.player.gold += self.current_enemy.gold_reward

        self.add_story_text(exp_msg)
        self.add_story_text(f"💰 Found {self.current_enemy.gold_reward} gold!")

        # Special rewards for specific enemies
        if self.current_enemy.name == "Ancient Dragon":
            self.player.add_item("Dragon Scale Armor")
            self.player.defense += 10
            self.add_story_text("🏆 You are now a legendary dragon slayer!")
            self.add_story_text("🛡️ Dragon Scale Armor increases your defense!")

        self.current_enemy = None
        self.update_stats_display()

        actions = [
            ("🏠 Return to Crossroads", self.show_crossroads),
            ("🏔️ Continue Adventure", self.mountain_path)
        ]
        self.create_action_buttons(actions)

    def combat_defeat(self):
        """Handle combat defeat."""
        self.add_story_text("💀 You have been defeated!")
        self.game_over()

    def player_defend(self):
        """Player defends, reducing incoming damage."""
        self.add_story_text("🛡️ You brace for impact!")
        self.defend_bonus = 5  # Reduce next incoming damage
        self.enemy_attack_with_defense()

    def enemy_attack_with_defense(self):
        """Enemy attacks with player defending."""
        enemy_damage = random.randint(self.current_enemy.attack - 1, self.current_enemy.attack + 1)
        enemy_damage = max(1, enemy_damage - 5)  # Defense bonus

        actual_damage = max(1, enemy_damage - self.player.defense)
        self.player.health -= actual_damage

        self.add_story_text(f"🐉 {self.current_enemy.name} attacks!")
        self.add_story_text(f"🛡️ Your defense reduces the damage!")
        self.add_story_text(f"💥 You take {actual_damage} damage! Health: {self.player.health}/{self.player.max_health}")

        self.update_stats_display()

        if self.player.health <= 0:
            self.combat_defeat()
        else:
            self.combat_turn()

    def use_item_in_combat(self):
        """Use an item during combat."""
        if "Health Potion" in self.player.inventory:
            self.player.inventory.remove("Health Potion")
            heal_msg = self.player.heal(40)
            self.add_story_text("🧪 You drink a health potion!")
            self.add_story_text(heal_msg)
            self.update_stats_display()
            self.enemy_attack()
        else:
            self.add_story_text("❌ You don't have any usable items!")
            self.combat_turn()

    def try_run_from_combat(self):
        """Try to run away from combat."""
        if random.random() < 0.7:  # 70% success rate
            self.add_story_text("💨 You successfully ran away!")
            self.current_enemy = None
            self.show_crossroads()
        else:
            self.add_story_text("❌ Couldn't escape!")
            self.enemy_attack()

    def try_run_away(self):
        """Try to run away from wolf encounter."""
        if random.random() < 0.8:  # 80% success rate outside combat
            self.add_story_text("💨 You successfully ran away from the wolf!")
            self.show_crossroads()
        else:
            self.add_story_text("❌ The wolf catches up to you!")
            self.start_combat()

    def try_tame_wolf(self):
        """Try to tame the wolf with food."""
        if self.player.has_item("Food") or self.player.has_item("Meat"):
            self.add_story_text("🥩 You offer food to the wolf...")
            self.add_story_text("🐺 The wolf accepts your offering and becomes friendly!")
            self.player.add_item("Wolf Companion")
            self.player.attack += 3
            self.add_story_text("🐺 The wolf decides to join you on your adventure!")
            self.add_story_text("⚔️ Your attack power increased!")

            if self.player.has_item("Food"):
                self.player.inventory.remove("Food")
            elif self.player.has_item("Meat"):
                self.player.inventory.remove("Meat")
        else:
            self.add_story_text("❌ You don't have any food to offer!")
            self.add_story_text("🐺 The wolf growls and prepares to attack!")
            self.start_combat()
            return

        self.update_stats_display()
        actions = [
            ("🏠 Return to Crossroads", self.show_crossroads),
            ("🌲 Continue in Forest", self.forest_path)
        ]
        self.create_action_buttons(actions)

    def troll_encounter(self):
        """Encounter with mountain troll."""
        self.add_story_text("👹 A massive troll blocks the mountain pass!")
        self.add_story_text("'None shall pass without paying the toll!' he rumbles.")

        actions = [
            ("💰 Pay Toll (30 gold)", self.pay_troll),
            ("⚔️ Fight the Troll", self.fight_troll),
            ("🧩 Challenge to Riddle", self.riddle_challenge)
        ]

        self.create_action_buttons(actions)

    def pay_troll(self):
        """Pay the troll's toll."""
        if self.player.gold >= 30:
            self.player.gold -= 30
            self.add_story_text("💰 You pay the troll 30 gold.")
            self.add_story_text("👹 'Smart choice, little one.' The troll steps aside.")
            self.update_stats_display()

            actions = [
                ("🏠 Return to Crossroads", self.show_crossroads),
                ("🏔️ Continue on Mountain", self.mountain_path)
            ]
            self.create_action_buttons(actions)
        else:
            self.add_story_text("💸 You don't have enough gold!")
            self.add_story_text("👹 'Then you must fight!' The troll attacks!")
            self.fight_troll()

    def fight_troll(self):
        """Fight the mountain troll."""
        self.current_enemy = Enemy("Mountain Troll", 80, 18, 6, 60, 40)
        self.start_combat()

    def riddle_challenge(self):
        """Challenge the troll to a riddle contest."""
        riddles = [
            ("What has keys but no locks, space but no room, and you can enter but not go inside?", "keyboard"),
            ("I speak without a mouth and hear without ears. What am I?", "echo"),
            ("The more you take, the more you leave behind. What am I?", "footsteps")
        ]

        riddle, answer = random.choice(riddles)
        self.add_story_text(f"👹 The troll grins: '{riddle}'")

        player_answer = simpledialog.askstring("Riddle Challenge", f"The troll asks:\n\n{riddle}\n\nYour answer:")

        if player_answer and answer.lower() in player_answer.lower():
            self.add_story_text("🎉 Correct! The troll is impressed!")
            self.add_story_text("👹 'You are wise, traveler. Take this gift.'")
            self.player.add_item("Troll's Wisdom Stone")
            exp_msg = self.player.gain_experience(30)
            self.add_story_text(exp_msg)
            self.update_stats_display()

            actions = [
                ("🏠 Return to Crossroads", self.show_crossroads),
                ("🏔️ Continue on Mountain", self.mountain_path)
            ]
            self.create_action_buttons(actions)
        else:
            self.add_story_text(f"❌ Wrong! The answer was '{answer}'.")
            self.add_story_text("👹 The troll attacks in anger!")
            self.fight_troll()

    def shrine_encounter(self):
        """Ancient shrine encounter."""
        self.add_story_text("⛩️ You discover an ancient shrine carved into the mountainside.")
        self.add_story_text("Strange runes glow softly on its surface, pulsing with mystical energy.")

        actions = [
            ("🙏 Pray at Shrine", self.pray_at_shrine),
            ("📚 Study the Runes", self.study_runes),
            ("💰 Make an Offering", self.make_offering),
            ("🏠 Return to Crossroads", self.show_crossroads)
        ]

        self.create_action_buttons(actions)

    def pray_at_shrine(self):
        """Pray at the ancient shrine."""
        self.add_story_text("🙏 You kneel and pray at the shrine...")

        blessing = random.choice(["health", "strength", "wisdom"])

        if blessing == "health":
            self.player.max_health += 20
            heal_msg = self.player.heal(20)
            self.add_story_text("✨ The shrine blesses you with vitality!")
            self.add_story_text(heal_msg)
        elif blessing == "strength":
            self.player.attack += 5
            self.add_story_text("✨ The shrine blesses you with strength!")
            self.add_story_text("⚔️ Your attack power increased!")
        else:
            exp_msg = self.player.gain_experience(50)
            self.add_story_text("✨ The shrine blesses you with wisdom!")
            self.add_story_text(exp_msg)

        self.update_stats_display()
        self.continue_from_shrine()

    def study_runes(self):
        """Study the ancient runes."""
        self.add_story_text("📚 You carefully study the ancient runes...")
        self.player.add_item("Ancient Knowledge")
        exp_msg = self.player.gain_experience(40)
        self.add_story_text("🧠 You gain understanding of the ancient language!")
        self.add_story_text(exp_msg)

        self.update_stats_display()
        self.continue_from_shrine()

    def make_offering(self):
        """Make an offering at the shrine."""
        if self.player.gold >= 20:
            self.player.gold -= 20
            self.add_story_text("💰 You offer 20 gold to the shrine...")
            self.add_story_text("✨ The shrine accepts your offering and grants a powerful blessing!")
            self.player.attack += 3
            self.player.defense += 3
            self.player.max_health += 15
            self.add_story_text("💪 All your abilities have been enhanced!")
            self.update_stats_display()
        else:
            self.add_story_text("💸 You don't have enough gold for a proper offering.")

        self.continue_from_shrine()

    def continue_from_shrine(self):
        """Continue after shrine encounter."""
        actions = [
            ("🏠 Return to Crossroads", self.show_crossroads),
            ("🏔️ Continue on Mountain", self.mountain_path)
        ]
        self.create_action_buttons(actions)

    def desert_path(self):
        """Desert adventure path."""
        self.player.current_location = "desert"
        self.update_image("desert")

        self.add_story_text("🏜️ You venture into the vast desert...")
        self.add_story_text("The sun beats down mercilessly as sand stretches endlessly.")

        # Check for water
        if not self.player.has_item("Water Flask"):
            self.add_story_text("💧 The heat is overwhelming! You need water urgently.")
            if random.random() < 0.4:  # 40% chance of finding oasis
                self.oasis_encounter()
                return
            else:
                damage_msg = self.player.take_damage(20)
                self.add_story_text("🔥 You suffer from dehydration!")
                self.add_story_text(damage_msg)
                self.update_stats_display()

                if self.player.health <= 0:
                    self.game_over()
                    return

        # Desert encounters
        encounter_type = random.choice(["sandstorm", "bandits", "pyramid"])

        if encounter_type == "sandstorm":
            self.sandstorm_encounter()
        elif encounter_type == "bandits":
            self.bandit_encounter()
        else:
            self.pyramid_encounter()

    def oasis_encounter(self):
        """Desert oasis encounter."""
        self.add_story_text("🌴 Miraculously, you spot an oasis in the distance!")
        self.add_story_text("You reach the oasis and drink deeply from the cool water.")
        heal_msg = self.player.heal(40)
        self.player.add_item("Water Flask")
        self.add_story_text(heal_msg)
        self.add_story_text("💧 You fill your water flask for the journey ahead.")

        self.update_stats_display()

        actions = [
            ("🏠 Return to Crossroads", self.show_crossroads),
            ("🏜️ Continue in Desert", self.desert_path)
        ]
        self.create_action_buttons(actions)

    def game_over(self):
        """Handle game over."""
        self.add_story_text("💀 GAME OVER")
        self.add_story_text(f"⚰️ {self.player.name} has fallen...")

        result = messagebox.askyesno("Game Over",
                                   f"💀 GAME OVER\n\n{self.player.name} has fallen in battle.\n\nWould you like to start a new adventure?")

        if result:
            self.start_game()
        else:
            self.root.quit()

    def check_victory(self):
        """Check if player has achieved victory."""
        if self.player.level >= 5:
            self.victory_ending()

    def victory_ending(self):
        """Handle victory ending."""
        self.update_image("victory")
        self.add_story_text("🏆 VICTORY! You have become a legendary adventurer!")
        self.add_story_text("Your tales will be told for generations to come!")

        result = messagebox.askyesno("Victory!",
                                   f"🏆 CONGRATULATIONS!\n\n{self.player.name} has become a legendary adventurer!\n\nWould you like to start a new adventure?")

        if result:
            self.start_game()
        else:
            self.root.quit()

# Placeholder methods for remaining encounters
    def sandstorm_encounter(self):
        """Sandstorm encounter - simplified for space."""
        self.add_story_text("🌪️ A massive sandstorm approaches!")
        self.add_story_text("You take shelter and wait it out.")
        actions = [("🏠 Return to Crossroads", self.show_crossroads)]
        self.create_action_buttons(actions)

    def bandit_encounter(self):
        """Bandit encounter - simplified for space."""
        self.add_story_text("🏴‍☠️ Desert bandits emerge from behind sand dunes!")
        self.current_enemy = Enemy("Desert Bandit", 50, 14, 3, 35, 25)
        self.start_combat()

    def pyramid_encounter(self):
        """Pyramid encounter - simplified for space."""
        self.add_story_text("🔺 You discover an ancient pyramid half-buried in sand!")
        self.add_story_text("Inside, you find ancient treasures!")
        self.player.gold += random.randint(40, 80)
        self.update_stats_display()
        actions = [("🏠 Return to Crossroads", self.show_crossroads)]
        self.create_action_buttons(actions)

# Main execution
def main():
    """Main function to run the game."""
    try:
        game = GraphicAdventureGame()
        game.run()
    except Exception as e:
        print(f"Error running the game: {e}")
        print("Make sure you have tkinter installed (usually comes with Python)")

if __name__ == "__main__":
    main()