import curses
import random
import time

def matrix_rain(stdscr):
    # Set up the screen
    stdscr.nodelay(True)
    stdscr.timeout(100)
    curses.curs_set(0) # Hide the cursor
    
    sh, sw = stdscr.getmaxyx()
    
    # Create streams of falling characters
    streams = [0] * sw
    
    # Initialize color pairs
    curses.start_color()
    curses.init_pair(1, curses.COLOR_GREEN, curses.COLOR_BLACK)
    curses.init_pair(2, curses.COLOR_WHITE, curses.COLOR_BLACK)
    curses.init_pair(3, curses.COLOR_CYAN, curses.COLOR_BLACK)
    
    # Main animation loop
    while True:
        for i in range(sw):
            if streams[i] == 0 or random.random() < 0.05:
                streams[i] = random.randint(1, sh - 1)
            
            # Use random hex characters for a more authentic feel
            char = random.choice('0123456789ABCDEF')
            
            try:
                # Draw the green stream
                stdscr.addch(streams[i], i, char, curses.color_pair(1))
                
                # Draw a brighter character at the top of the stream
                if streams[i] > 0:
                    stdscr.addch(streams[i] - 1, i, char, curses.color_pair(2))
                
            except curses.error:
                # Ignore errors when characters go off screen
                pass
            
            # Reset the stream if it goes past the screen height
            if streams[i] >= sh - 1:
                streams[i] = 0
            else:
                streams[i] += 1
                
        # Refresh the screen to show the new frame
        stdscr.refresh()
        # Control the speed of the animation
        time.sleep(0.05)

if __name__ == '__main__':
    # Wrap the main function in a curses wrapper to handle setup and teardown
    curses.wrapper(matrix_rain)