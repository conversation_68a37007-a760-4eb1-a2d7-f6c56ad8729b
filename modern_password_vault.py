#!/usr/bin/env python3
"""
🔐 Modern Password Vault
A sleek, modern password manager with curved GUI design
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
import secrets
import string
import hashlib
import base64

class ModernPasswordVault:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔐 Modern Password Vault")
        self.root.geometry("1000x700")
        self.root.configure(bg='#0f0f23')
        
        # Modern color palette
        self.colors = {
            'bg_primary': '#0f0f23',
            'bg_secondary': '#1a1a2e',
            'bg_card': '#16213e',
            'bg_input': '#0f3460',
            'accent_primary': '#e94560',
            'accent_secondary': '#f38ba8',
            'success': '#a6e3a1',
            'warning': '#f9e2af',
            'danger': '#f38ba8',
            'text_primary': '#cdd6f4',
            'text_secondary': '#a6adc8',
            'text_muted': '#6c7086',
            'border': '#313244'
        }
        
        # Data storage
        self.data_file = "vault_data.json"
        self.passwords = {}
        self.master_password = None
        
        # Setup modern styling
        self.setup_modern_style()
        self.create_login_screen()
    
    def setup_modern_style(self):
        """Setup modern ttk styling."""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure modern button style
        style.configure('Modern.TButton',
                       background=self.colors['accent_primary'],
                       foreground='white',
                       font=('Segoe UI', 10, 'bold'),
                       borderwidth=0,
                       focuscolor='none',
                       relief='flat')
        
        style.map('Modern.TButton',
                 background=[('active', self.colors['accent_secondary']),
                           ('pressed', '#d63384')])
        
        # Configure modern entry style
        style.configure('Modern.TEntry',
                       fieldbackground=self.colors['bg_input'],
                       foreground=self.colors['text_primary'],
                       borderwidth=1,
                       relief='flat',
                       insertcolor=self.colors['text_primary'])
        
        # Configure modern frame style
        style.configure('Card.TFrame',
                       background=self.colors['bg_card'],
                       relief='flat',
                       borderwidth=1)
    
    def create_modern_card(self, parent, width=400, height=300):
        """Create a modern card-style frame."""
        card_frame = tk.Frame(parent, bg=self.colors['bg_card'], 
                             relief='flat', bd=0)
        card_frame.configure(highlightbackground=self.colors['border'],
                           highlightthickness=1)
        return card_frame
    
    def create_modern_button(self, parent, text, command, **kwargs):
        """Create a modern styled button."""
        default_style = {
            'bg': self.colors['accent_primary'],
            'fg': 'white',
            'font': ('Segoe UI', 10, 'bold'),
            'relief': 'flat',
            'bd': 0,
            'padx': 20,
            'pady': 10,
            'cursor': 'hand2'
        }
        default_style.update(kwargs)
        
        btn = tk.Button(parent, text=text, command=command, **default_style)
        
        # Add hover effects
        def on_enter(e):
            btn.configure(bg=self.colors['accent_secondary'])
        def on_leave(e):
            btn.configure(bg=default_style['bg'])
        
        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)
        
        return btn
    
    def create_modern_entry(self, parent, placeholder="", show=None, **kwargs):
        """Create a modern styled entry with placeholder."""
        default_style = {
            'bg': self.colors['bg_input'],
            'fg': self.colors['text_primary'],
            'font': ('Segoe UI', 11),
            'relief': 'flat',
            'bd': 0,
            'insertbackground': self.colors['text_primary']
        }
        if show:
            default_style['show'] = show
        default_style.update(kwargs)
        
        entry = tk.Entry(parent, **default_style)
        
        # Add placeholder functionality
        if placeholder:
            entry.insert(0, placeholder)
            entry.configure(fg=self.colors['text_muted'])
            
            def on_focus_in(event):
                if entry.get() == placeholder:
                    entry.delete(0, tk.END)
                    entry.configure(fg=self.colors['text_primary'])
            
            def on_focus_out(event):
                if not entry.get():
                    entry.insert(0, placeholder)
                    entry.configure(fg=self.colors['text_muted'])
            
            entry.bind('<FocusIn>', on_focus_in)
            entry.bind('<FocusOut>', on_focus_out)
        
        return entry
    
    def create_login_screen(self):
        """Create the modern login screen."""
        # Clear window
        for widget in self.root.winfo_children():
            widget.destroy()
        
        # Main container with gradient-like effect
        main_container = tk.Frame(self.root, bg=self.colors['bg_primary'])
        main_container.pack(fill=tk.BOTH, expand=True)
        
        # Center the login card
        center_frame = tk.Frame(main_container, bg=self.colors['bg_primary'])
        center_frame.place(relx=0.5, rely=0.5, anchor='center')
        
        # Login card
        login_card = self.create_modern_card(center_frame, 450, 500)
        login_card.pack(padx=40, pady=40)
        
        # Header section
        header_frame = tk.Frame(login_card, bg=self.colors['bg_card'])
        header_frame.pack(fill=tk.X, pady=(30, 20))
        
        # App icon and title
        title_label = tk.Label(header_frame, text="🔐", font=('Segoe UI', 48),
                              bg=self.colors['bg_card'], fg=self.colors['accent_primary'])
        title_label.pack()
        
        app_title = tk.Label(header_frame, text="Password Vault", 
                            font=('Segoe UI', 24, 'bold'),
                            bg=self.colors['bg_card'], fg=self.colors['text_primary'])
        app_title.pack(pady=(10, 5))
        
        subtitle = tk.Label(header_frame, text="Secure • Modern • Simple", 
                           font=('Segoe UI', 12),
                           bg=self.colors['bg_card'], fg=self.colors['text_secondary'])
        subtitle.pack()
        
        # Login form
        form_frame = tk.Frame(login_card, bg=self.colors['bg_card'])
        form_frame.pack(fill=tk.X, padx=40, pady=20)
        
        # Master password label
        password_label = tk.Label(form_frame, text="Master Password", 
                                 font=('Segoe UI', 12, 'bold'),
                                 bg=self.colors['bg_card'], fg=self.colors['text_primary'])
        password_label.pack(anchor='w', pady=(0, 8))
        
        # Password entry
        self.master_entry = self.create_modern_entry(form_frame, 
                                                    placeholder="Enter your master password",
                                                    show="*", width=35)
        self.master_entry.pack(fill=tk.X, ipady=12)
        
        # Button section
        button_frame = tk.Frame(login_card, bg=self.colors['bg_card'])
        button_frame.pack(fill=tk.X, padx=40, pady=(30, 40))
        
        # Login button
        login_btn = self.create_modern_button(button_frame, "🔓 Unlock Vault", 
                                             self.login, width=25)
        login_btn.pack(fill=tk.X, pady=(0, 10), ipady=5)
        
        # Create vault button
        create_btn = self.create_modern_button(button_frame, "✨ Create New Vault", 
                                              self.create_new_vault, 
                                              bg=self.colors['bg_secondary'],
                                              width=25)
        create_btn.pack(fill=tk.X, ipady=5)
        
        # Footer
        footer_frame = tk.Frame(login_card, bg=self.colors['bg_card'])
        footer_frame.pack(fill=tk.X, pady=(20, 30))
        
        footer_text = tk.Label(footer_frame, text="Your passwords are encrypted and stored locally", 
                              font=('Segoe UI', 9),
                              bg=self.colors['bg_card'], fg=self.colors['text_muted'])
        footer_text.pack()
        
        # Bind Enter key
        self.master_entry.bind('<Return>', lambda e: self.login())
        self.master_entry.focus()
    
    def hash_password(self, password):
        """Simple password hashing."""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def login(self):
        """Handle login."""
        password = self.master_entry.get()
        if not password or password == "Enter your master password":
            self.show_notification("Please enter your master password!", "warning")
            return
        
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r') as f:
                    data = json.load(f)
                
                stored_hash = data.get('master_hash')
                if stored_hash == self.hash_password(password):
                    self.passwords = data.get('passwords', {})
                    self.master_password = password
                    self.create_main_interface()
                    self.show_notification("Vault unlocked successfully!", "success")
                else:
                    self.show_notification("Invalid master password!", "danger")
            except Exception as e:
                self.show_notification(f"Error loading vault: {e}", "danger")
        else:
            self.show_notification("No vault found. Create a new one!", "warning")
    
    def create_new_vault(self):
        """Create new vault."""
        password = self.master_entry.get()
        if not password or password == "Enter your master password":
            self.show_notification("Please enter a master password!", "warning")
            return
        
        if len(password) < 6:
            self.show_notification("Master password must be at least 6 characters!", "warning")
            return
        
        self.passwords = {}
        self.master_password = password
        self.save_data()
        self.show_notification("Vault created successfully!", "success")
        self.create_main_interface()
    
    def save_data(self):
        """Save password data."""
        data = {
            'master_hash': self.hash_password(self.master_password),
            'passwords': self.passwords
        }
        
        with open(self.data_file, 'w') as f:
            json.dump(data, f, indent=2)
    
    def show_notification(self, message, type_="info"):
        """Show a modern notification."""
        colors = {
            'success': self.colors['success'],
            'warning': self.colors['warning'],
            'danger': self.colors['danger'],
            'info': self.colors['accent_primary']
        }
        
        # Create notification window
        notification = tk.Toplevel(self.root)
        notification.title("")
        notification.geometry("350x100")
        notification.configure(bg=colors.get(type_, self.colors['accent_primary']))
        notification.resizable(False, False)
        notification.transient(self.root)
        
        # Center notification
        notification.geometry("+%d+%d" % (
            self.root.winfo_rootx() + 325,
            self.root.winfo_rooty() + 50
        ))
        
        # Notification content
        content_frame = tk.Frame(notification, bg=colors.get(type_, self.colors['accent_primary']))
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Icon
        icons = {'success': '✅', 'warning': '⚠️', 'danger': '❌', 'info': 'ℹ️'}
        icon_label = tk.Label(content_frame, text=icons.get(type_, 'ℹ️'), 
                             font=('Segoe UI', 20),
                             bg=colors.get(type_, self.colors['accent_primary']), 
                             fg='white')
        icon_label.pack(side=tk.LEFT, padx=(0, 15))
        
        # Message
        msg_label = tk.Label(content_frame, text=message, 
                            font=('Segoe UI', 11, 'bold'),
                            bg=colors.get(type_, self.colors['accent_primary']), 
                            fg='white', wraplength=250)
        msg_label.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Auto close after 3 seconds
        notification.after(3000, notification.destroy)
    
    def create_main_interface(self):
        """Create the main vault interface."""
        # Clear window
        for widget in self.root.winfo_children():
            widget.destroy()
        
        # Main container
        main_frame = tk.Frame(self.root, bg=self.colors['bg_primary'])
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Header
        self.create_header(main_frame)
        
        # Content area
        content_frame = tk.Frame(main_frame, bg=self.colors['bg_primary'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))
        
        # Search and actions bar
        self.create_action_bar(content_frame)
        
        # Passwords display area
        self.create_passwords_area(content_frame)
        
        # Load passwords
        self.refresh_passwords_display()
    
    def create_header(self, parent):
        """Create the modern header."""
        header_frame = tk.Frame(parent, bg=self.colors['bg_secondary'], height=80)
        header_frame.pack(fill=tk.X, padx=20, pady=(20, 0))
        header_frame.pack_propagate(False)
        
        # Left side - Title
        left_frame = tk.Frame(header_frame, bg=self.colors['bg_secondary'])
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=20)
        
        title_label = tk.Label(left_frame, text="🔐 Password Vault", 
                              font=('Segoe UI', 18, 'bold'),
                              bg=self.colors['bg_secondary'], fg=self.colors['text_primary'])
        title_label.pack(anchor='w', pady=(15, 5))
        
        subtitle_label = tk.Label(left_frame, text=f"{len(self.passwords)} passwords stored", 
                                 font=('Segoe UI', 10),
                                 bg=self.colors['bg_secondary'], fg=self.colors['text_secondary'])
        subtitle_label.pack(anchor='w')
        
        # Right side - Actions
        right_frame = tk.Frame(header_frame, bg=self.colors['bg_secondary'])
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=20)
        
        self.create_modern_button(right_frame, "➕ Add Password", 
                                 self.show_add_dialog, width=15).pack(side=tk.RIGHT, padx=5, pady=20)
        
        self.create_modern_button(right_frame, "🎲 Generate", 
                                 self.show_generate_dialog, 
                                 bg=self.colors['success'], width=12).pack(side=tk.RIGHT, padx=5, pady=20)
        
        self.create_modern_button(right_frame, "🚪 Logout",
                                 self.logout,
                                 bg=self.colors['danger'], width=10).pack(side=tk.RIGHT, padx=5, pady=20)

    def create_action_bar(self, parent):
        """Create search and filter bar."""
        action_frame = tk.Frame(parent, bg=self.colors['bg_secondary'], height=60)
        action_frame.pack(fill=tk.X, pady=(20, 0))
        action_frame.pack_propagate(False)

        # Search section
        search_frame = tk.Frame(action_frame, bg=self.colors['bg_secondary'])
        search_frame.pack(side=tk.LEFT, fill=tk.Y, padx=20)

        search_label = tk.Label(search_frame, text="🔍", font=('Segoe UI', 16),
                               bg=self.colors['bg_secondary'], fg=self.colors['text_secondary'])
        search_label.pack(side=tk.LEFT, pady=15, padx=(0, 10))

        self.search_var = tk.StringVar()
        self.search_entry = self.create_modern_entry(search_frame,
                                                    placeholder="Search passwords...",
                                                    width=30, textvariable=self.search_var)
        self.search_entry.pack(side=tk.LEFT, pady=15, ipady=8)
        self.search_var.trace('w', self.filter_passwords)

        # Filter buttons
        filter_frame = tk.Frame(action_frame, bg=self.colors['bg_secondary'])
        filter_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=20)

        self.create_modern_button(filter_frame, "All",
                                 lambda: self.set_filter("all"),
                                 bg=self.colors['bg_input'], width=8).pack(side=tk.RIGHT, padx=2, pady=15)

        self.create_modern_button(filter_frame, "Weak",
                                 lambda: self.set_filter("weak"),
                                 bg=self.colors['warning'], width=8).pack(side=tk.RIGHT, padx=2, pady=15)

        self.create_modern_button(filter_frame, "Strong",
                                 lambda: self.set_filter("strong"),
                                 bg=self.colors['success'], width=8).pack(side=tk.RIGHT, padx=2, pady=15)

    def create_passwords_area(self, parent):
        """Create the passwords display area."""
        # Scrollable frame for password cards
        canvas_frame = tk.Frame(parent, bg=self.colors['bg_primary'])
        canvas_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))

        # Canvas and scrollbar
        self.canvas = tk.Canvas(canvas_frame, bg=self.colors['bg_primary'],
                               highlightthickness=0)
        scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = tk.Frame(self.canvas, bg=self.colors['bg_primary'])

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar.set)

        self.canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Bind mousewheel
        def _on_mousewheel(event):
            self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        self.canvas.bind_all("<MouseWheel>", _on_mousewheel)

    def create_password_card(self, parent, site, data):
        """Create a modern password card."""
        card = self.create_modern_card(parent, 800, 100)
        card.pack(fill=tk.X, pady=10, padx=20)

        # Main content frame
        content_frame = tk.Frame(card, bg=self.colors['bg_card'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=15)

        # Left side - Site info
        left_frame = tk.Frame(content_frame, bg=self.colors['bg_card'])
        left_frame.pack(side=tk.LEFT, fill=tk.Y)

        # Site icon and name
        icon_frame = tk.Frame(left_frame, bg=self.colors['bg_card'])
        icon_frame.pack(side=tk.LEFT, padx=(0, 15))

        # Generate site icon
        site_icon = self.get_site_icon(site)
        icon_label = tk.Label(icon_frame, text=site_icon, font=('Segoe UI', 24),
                             bg=self.colors['bg_card'], fg=self.colors['accent_primary'])
        icon_label.pack()

        # Site details
        details_frame = tk.Frame(left_frame, bg=self.colors['bg_card'])
        details_frame.pack(side=tk.LEFT, fill=tk.Y)

        site_label = tk.Label(details_frame, text=site,
                             font=('Segoe UI', 14, 'bold'),
                             bg=self.colors['bg_card'], fg=self.colors['text_primary'])
        site_label.pack(anchor='w')

        username_label = tk.Label(details_frame, text=f"👤 {data.get('username', 'N/A')}",
                                 font=('Segoe UI', 10),
                                 bg=self.colors['bg_card'], fg=self.colors['text_secondary'])
        username_label.pack(anchor='w')

        # Password strength indicator
        strength = self.get_password_strength(data.get('password', ''))
        strength_colors = {'Weak': self.colors['danger'], 'Medium': self.colors['warning'], 'Strong': self.colors['success']}
        strength_label = tk.Label(details_frame, text=f"🔒 {strength}",
                                 font=('Segoe UI', 9, 'bold'),
                                 bg=self.colors['bg_card'], fg=strength_colors.get(strength, self.colors['text_muted']))
        strength_label.pack(anchor='w')

        # Right side - Actions
        actions_frame = tk.Frame(content_frame, bg=self.colors['bg_card'])
        actions_frame.pack(side=tk.RIGHT, fill=tk.Y)

        # Action buttons
        self.create_modern_button(actions_frame, "👁️",
                                 lambda s=site: self.view_password(s),
                                 bg=self.colors['bg_input'], width=3).pack(side=tk.RIGHT, padx=2)

        self.create_modern_button(actions_frame, "📋",
                                 lambda s=site: self.copy_password(s),
                                 bg=self.colors['success'], width=3).pack(side=tk.RIGHT, padx=2)

        self.create_modern_button(actions_frame, "✏️",
                                 lambda s=site: self.edit_password(s),
                                 bg=self.colors['accent_primary'], width=3).pack(side=tk.RIGHT, padx=2)

        self.create_modern_button(actions_frame, "🗑️",
                                 lambda s=site: self.delete_password(s),
                                 bg=self.colors['danger'], width=3).pack(side=tk.RIGHT, padx=2)

    def get_site_icon(self, site):
        """Get an icon for the site."""
        site_lower = site.lower()
        icons = {
            'google': '🌐', 'gmail': '📧', 'facebook': '📘', 'twitter': '🐦',
            'instagram': '📷', 'linkedin': '💼', 'github': '🐙', 'amazon': '📦',
            'netflix': '🎬', 'spotify': '🎵', 'youtube': '📺', 'discord': '🎮',
            'reddit': '🤖', 'pinterest': '📌', 'dropbox': '📁', 'microsoft': '🪟',
            'apple': '🍎', 'steam': '🎮', 'paypal': '💳', 'bank': '🏦'
        }

        for key, icon in icons.items():
            if key in site_lower:
                return icon
        return '🔐'

    def get_password_strength(self, password):
        """Analyze password strength."""
        if len(password) < 6:
            return "Weak"

        score = 0
        if len(password) >= 8:
            score += 1
        if any(c.isupper() for c in password):
            score += 1
        if any(c.islower() for c in password):
            score += 1
        if any(c.isdigit() for c in password):
            score += 1
        if any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            score += 1

        if score >= 4:
            return "Strong"
        elif score >= 2:
            return "Medium"
        else:
            return "Weak"

    def refresh_passwords_display(self):
        """Refresh the passwords display."""
        # Clear existing cards
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

        # Filter passwords based on search and filter
        filtered_passwords = self.get_filtered_passwords()

        if not filtered_passwords:
            # Show empty state
            empty_frame = tk.Frame(self.scrollable_frame, bg=self.colors['bg_primary'])
            empty_frame.pack(fill=tk.BOTH, expand=True, pady=100)

            empty_icon = tk.Label(empty_frame, text="🔍", font=('Segoe UI', 48),
                                 bg=self.colors['bg_primary'], fg=self.colors['text_muted'])
            empty_icon.pack()

            empty_text = tk.Label(empty_frame, text="No passwords found",
                                 font=('Segoe UI', 16, 'bold'),
                                 bg=self.colors['bg_primary'], fg=self.colors['text_muted'])
            empty_text.pack(pady=(10, 5))

            empty_subtitle = tk.Label(empty_frame, text="Try adjusting your search or add a new password",
                                     font=('Segoe UI', 12),
                                     bg=self.colors['bg_primary'], fg=self.colors['text_muted'])
            empty_subtitle.pack()
        else:
            # Create password cards
            for site, data in filtered_passwords.items():
                self.create_password_card(self.scrollable_frame, site, data)

    def get_filtered_passwords(self):
        """Get filtered passwords based on search and filter."""
        filtered = {}
        search_term = self.search_var.get().lower() if hasattr(self, 'search_var') else ""

        for site, data in self.passwords.items():
            # Apply search filter
            if search_term and search_term not in site.lower() and search_term not in data.get('username', '').lower():
                continue

            # Apply strength filter
            if hasattr(self, 'current_filter') and self.current_filter != "all":
                strength = self.get_password_strength(data.get('password', '')).lower()
                if self.current_filter == "strong" and strength != "strong":
                    continue
                elif self.current_filter == "weak" and strength != "weak":
                    continue

            filtered[site] = data

        return filtered

    def filter_passwords(self, *args):
        """Filter passwords based on search."""
        self.refresh_passwords_display()

    def set_filter(self, filter_type):
        """Set the current filter."""
        self.current_filter = filter_type
        self.refresh_passwords_display()

    def show_add_dialog(self):
        """Show add password dialog."""
        self.show_password_dialog("Add New Password")

    def show_password_dialog(self, title, site="", username="", password="", url=""):
        """Show modern password dialog."""
        dialog = tk.Toplevel(self.root)
        dialog.title(title)
        dialog.geometry("500x600")
        dialog.configure(bg=self.colors['bg_primary'])
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # Center dialog
        dialog.geometry("+%d+%d" % (
            self.root.winfo_rootx() + 250,
            self.root.winfo_rooty() + 50
        ))

        # Main card
        main_card = self.create_modern_card(dialog, 460, 560)
        main_card.pack(padx=20, pady=20)

        # Header
        header_frame = tk.Frame(main_card, bg=self.colors['bg_card'])
        header_frame.pack(fill=tk.X, pady=(20, 30))

        title_label = tk.Label(header_frame, text=title,
                              font=('Segoe UI', 18, 'bold'),
                              bg=self.colors['bg_card'], fg=self.colors['text_primary'])
        title_label.pack()

        # Form
        form_frame = tk.Frame(main_card, bg=self.colors['bg_card'])
        form_frame.pack(fill=tk.X, padx=30, pady=20)

        # Site name
        tk.Label(form_frame, text="Site/Service Name",
                font=('Segoe UI', 11, 'bold'),
                bg=self.colors['bg_card'], fg=self.colors['text_primary']).pack(anchor='w', pady=(0, 5))

        site_entry = self.create_modern_entry(form_frame, placeholder="e.g., Google, Facebook", width=40)
        site_entry.pack(fill=tk.X, ipady=10, pady=(0, 15))
        if site:
            site_entry.delete(0, tk.END)
            site_entry.insert(0, site)

        # Username/Email
        tk.Label(form_frame, text="Username/Email",
                font=('Segoe UI', 11, 'bold'),
                bg=self.colors['bg_card'], fg=self.colors['text_primary']).pack(anchor='w', pady=(0, 5))

        username_entry = self.create_modern_entry(form_frame, placeholder="<EMAIL>", width=40)
        username_entry.pack(fill=tk.X, ipady=10, pady=(0, 15))
        if username:
            username_entry.delete(0, tk.END)
            username_entry.insert(0, username)

        # Password
        tk.Label(form_frame, text="Password",
                font=('Segoe UI', 11, 'bold'),
                bg=self.colors['bg_card'], fg=self.colors['text_primary']).pack(anchor='w', pady=(0, 5))

        password_frame = tk.Frame(form_frame, bg=self.colors['bg_card'])
        password_frame.pack(fill=tk.X, pady=(0, 15))

        password_entry = self.create_modern_entry(password_frame, show="*", width=35)
        password_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, ipady=10)
        if password:
            password_entry.insert(0, password)

        # Show/Hide password button
        show_var = tk.BooleanVar()
        def toggle_password():
            if show_var.get():
                password_entry.configure(show="")
                show_btn.configure(text="🙈")
            else:
                password_entry.configure(show="*")
                show_btn.configure(text="👁️")

        show_btn = self.create_modern_button(password_frame, "👁️", toggle_password,
                                           bg=self.colors['bg_input'], width=3)
        show_btn.pack(side=tk.RIGHT, padx=(10, 0), ipady=10)

        # Generate password button
        def generate_password():
            new_password = self.generate_secure_password()
            password_entry.delete(0, tk.END)
            password_entry.insert(0, new_password)
            self.show_notification("Secure password generated!", "success")

        self.create_modern_button(password_frame, "🎲", generate_password,
                                 bg=self.colors['success'], width=3).pack(side=tk.RIGHT, padx=(5, 0), ipady=10)

        # URL (optional)
        tk.Label(form_frame, text="Website URL (Optional)",
                font=('Segoe UI', 11, 'bold'),
                bg=self.colors['bg_card'], fg=self.colors['text_primary']).pack(anchor='w', pady=(0, 5))

        url_entry = self.create_modern_entry(form_frame, placeholder="https://example.com", width=40)
        url_entry.pack(fill=tk.X, ipady=10, pady=(0, 20))
        if url:
            url_entry.delete(0, tk.END)
            url_entry.insert(0, url)

        # Buttons
        button_frame = tk.Frame(main_card, bg=self.colors['bg_card'])
        button_frame.pack(fill=tk.X, padx=30, pady=(0, 30))

        def save_password():
            site_name = site_entry.get().strip()
            user_name = username_entry.get().strip()
            pass_word = password_entry.get()
            site_url = url_entry.get().strip()

            if not site_name or site_name == "e.g., Google, Facebook":
                self.show_notification("Please enter a site name!", "warning")
                return

            if not pass_word:
                self.show_notification("Please enter a password!", "warning")
                return

            self.passwords[site_name] = {
                'username': user_name,
                'password': pass_word,
                'url': site_url
            }

            self.save_data()
            self.refresh_passwords_display()
            self.show_notification(f"Password for {site_name} saved!", "success")
            dialog.destroy()

        self.create_modern_button(button_frame, "💾 Save Password", save_password,
                                 width=20).pack(side=tk.RIGHT, padx=(10, 0), ipady=8)

        self.create_modern_button(button_frame, "❌ Cancel", dialog.destroy,
                                 bg=self.colors['bg_input'], width=15).pack(side=tk.RIGHT, ipady=8)

        # Focus on site entry
        site_entry.focus()

    def show_generate_dialog(self):
        """Show password generator dialog."""
        dialog = tk.Toplevel(self.root)
        dialog.title("Password Generator")
        dialog.geometry("450x500")
        dialog.configure(bg=self.colors['bg_primary'])
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # Center dialog
        dialog.geometry("+%d+%d" % (
            self.root.winfo_rootx() + 275,
            self.root.winfo_rooty() + 100
        ))

        # Main card
        main_card = self.create_modern_card(dialog, 410, 460)
        main_card.pack(padx=20, pady=20)

        # Header
        header_frame = tk.Frame(main_card, bg=self.colors['bg_card'])
        header_frame.pack(fill=tk.X, pady=(20, 30))

        title_label = tk.Label(header_frame, text="🎲 Password Generator",
                              font=('Segoe UI', 18, 'bold'),
                              bg=self.colors['bg_card'], fg=self.colors['text_primary'])
        title_label.pack()

        # Options
        options_frame = tk.Frame(main_card, bg=self.colors['bg_card'])
        options_frame.pack(fill=tk.X, padx=30, pady=20)

        # Length
        tk.Label(options_frame, text="Password Length",
                font=('Segoe UI', 11, 'bold'),
                bg=self.colors['bg_card'], fg=self.colors['text_primary']).pack(anchor='w', pady=(0, 5))

        length_var = tk.IntVar(value=16)
        length_scale = tk.Scale(options_frame, from_=8, to=32, orient=tk.HORIZONTAL,
                               variable=length_var, bg=self.colors['bg_card'],
                               fg=self.colors['text_primary'], highlightthickness=0,
                               troughcolor=self.colors['bg_input'])
        length_scale.pack(fill=tk.X, pady=(0, 15))

        # Options checkboxes
        uppercase_var = tk.BooleanVar(value=True)
        lowercase_var = tk.BooleanVar(value=True)
        numbers_var = tk.BooleanVar(value=True)
        symbols_var = tk.BooleanVar(value=True)

        tk.Checkbutton(options_frame, text="Include Uppercase (A-Z)", variable=uppercase_var,
                      bg=self.colors['bg_card'], fg=self.colors['text_primary'],
                      selectcolor=self.colors['accent_primary']).pack(anchor='w', pady=2)

        tk.Checkbutton(options_frame, text="Include Lowercase (a-z)", variable=lowercase_var,
                      bg=self.colors['bg_card'], fg=self.colors['text_primary'],
                      selectcolor=self.colors['accent_primary']).pack(anchor='w', pady=2)

        tk.Checkbutton(options_frame, text="Include Numbers (0-9)", variable=numbers_var,
                      bg=self.colors['bg_card'], fg=self.colors['text_primary'],
                      selectcolor=self.colors['accent_primary']).pack(anchor='w', pady=2)

        tk.Checkbutton(options_frame, text="Include Symbols (!@#$%)", variable=symbols_var,
                      bg=self.colors['bg_card'], fg=self.colors['text_primary'],
                      selectcolor=self.colors['accent_primary']).pack(anchor='w', pady=15)

        # Generated password display
        tk.Label(options_frame, text="Generated Password",
                font=('Segoe UI', 11, 'bold'),
                bg=self.colors['bg_card'], fg=self.colors['text_primary']).pack(anchor='w', pady=(0, 5))

        password_display = tk.Text(options_frame, height=3, width=40,
                                  bg=self.colors['bg_input'], fg=self.colors['text_primary'],
                                  font=('Courier', 11, 'bold'), wrap=tk.WORD)
        password_display.pack(fill=tk.X, pady=(0, 15))

        def generate_custom_password():
            chars = ""
            if uppercase_var.get():
                chars += string.ascii_uppercase
            if lowercase_var.get():
                chars += string.ascii_lowercase
            if numbers_var.get():
                chars += string.digits
            if symbols_var.get():
                chars += "!@#$%^&*()_+-=[]{}|;:,.<>?"

            if not chars:
                self.show_notification("Please select at least one character type!", "warning")
                return

            password = ''.join(secrets.choice(chars) for _ in range(length_var.get()))
            password_display.delete(1.0, tk.END)
            password_display.insert(1.0, password)

        def copy_generated():
            password = password_display.get(1.0, tk.END).strip()
            if password:
                self.root.clipboard_clear()
                self.root.clipboard_append(password)
                self.show_notification("Password copied to clipboard!", "success")

        # Buttons
        button_frame = tk.Frame(main_card, bg=self.colors['bg_card'])
        button_frame.pack(fill=tk.X, padx=30, pady=(0, 30))

        self.create_modern_button(button_frame, "🎲 Generate", generate_custom_password,
                                 width=15).pack(side=tk.LEFT, ipady=8)

        self.create_modern_button(button_frame, "📋 Copy", copy_generated,
                                 bg=self.colors['success'], width=12).pack(side=tk.LEFT, padx=10, ipady=8)

        self.create_modern_button(button_frame, "❌ Close", dialog.destroy,
                                 bg=self.colors['bg_input'], width=12).pack(side=tk.RIGHT, ipady=8)

        # Generate initial password
        generate_custom_password()

    def generate_secure_password(self, length=16):
        """Generate a secure password."""
        chars = string.ascii_letters + string.digits + "!@#$%^&*()_+-="
        return ''.join(secrets.choice(chars) for _ in range(length))

    def view_password(self, site):
        """View password details."""
        if site not in self.passwords:
            self.show_notification("Password not found!", "danger")
            return

        data = self.passwords[site]

        # Create view dialog
        dialog = tk.Toplevel(self.root)
        dialog.title(f"View Password - {site}")
        dialog.geometry("400x350")
        dialog.configure(bg=self.colors['bg_primary'])
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # Center dialog
        dialog.geometry("+%d+%d" % (
            self.root.winfo_rootx() + 300,
            self.root.winfo_rooty() + 175
        ))

        # Main card
        main_card = self.create_modern_card(dialog, 360, 310)
        main_card.pack(padx=20, pady=20)

        # Header
        header_frame = tk.Frame(main_card, bg=self.colors['bg_card'])
        header_frame.pack(fill=tk.X, pady=(20, 30))

        icon = self.get_site_icon(site)
        icon_label = tk.Label(header_frame, text=icon, font=('Segoe UI', 32),
                             bg=self.colors['bg_card'], fg=self.colors['accent_primary'])
        icon_label.pack()

        title_label = tk.Label(header_frame, text=site,
                              font=('Segoe UI', 16, 'bold'),
                              bg=self.colors['bg_card'], fg=self.colors['text_primary'])
        title_label.pack(pady=(10, 0))

        # Details
        details_frame = tk.Frame(main_card, bg=self.colors['bg_card'])
        details_frame.pack(fill=tk.X, padx=30, pady=20)

        # Username
        tk.Label(details_frame, text="Username:",
                font=('Segoe UI', 10, 'bold'),
                bg=self.colors['bg_card'], fg=self.colors['text_secondary']).pack(anchor='w')

        username_frame = tk.Frame(details_frame, bg=self.colors['bg_input'])
        username_frame.pack(fill=tk.X, pady=(5, 15))

        tk.Label(username_frame, text=data.get('username', 'N/A'),
                font=('Segoe UI', 11),
                bg=self.colors['bg_input'], fg=self.colors['text_primary']).pack(pady=8, padx=10, anchor='w')

        # Password
        tk.Label(details_frame, text="Password:",
                font=('Segoe UI', 10, 'bold'),
                bg=self.colors['bg_card'], fg=self.colors['text_secondary']).pack(anchor='w')

        password_frame = tk.Frame(details_frame, bg=self.colors['bg_input'])
        password_frame.pack(fill=tk.X, pady=(5, 15))

        password_text = tk.Label(password_frame, text="••••••••••••",
                                font=('Segoe UI', 11),
                                bg=self.colors['bg_input'], fg=self.colors['text_primary'])
        password_text.pack(side=tk.LEFT, pady=8, padx=10)

        show_var = tk.BooleanVar()
        def toggle_password_view():
            if show_var.get():
                password_text.configure(text=data.get('password', ''))
                show_btn.configure(text="🙈")
            else:
                password_text.configure(text="••••••••••••")
                show_btn.configure(text="👁️")

        show_btn = self.create_modern_button(password_frame, "👁️", toggle_password_view,
                                           bg=self.colors['accent_primary'], width=3)
        show_btn.pack(side=tk.RIGHT, pady=5, padx=5)

        # URL
        if data.get('url'):
            tk.Label(details_frame, text="URL:",
                    font=('Segoe UI', 10, 'bold'),
                    bg=self.colors['bg_card'], fg=self.colors['text_secondary']).pack(anchor='w')

            url_frame = tk.Frame(details_frame, bg=self.colors['bg_input'])
            url_frame.pack(fill=tk.X, pady=(5, 15))

            tk.Label(url_frame, text=data.get('url', ''),
                    font=('Segoe UI', 11),
                    bg=self.colors['bg_input'], fg=self.colors['text_primary']).pack(pady=8, padx=10, anchor='w')

        # Buttons
        button_frame = tk.Frame(main_card, bg=self.colors['bg_card'])
        button_frame.pack(fill=tk.X, padx=30, pady=(0, 30))

        self.create_modern_button(button_frame, "📋 Copy Password",
                                 lambda: self.copy_password(site),
                                 bg=self.colors['success'], width=15).pack(side=tk.LEFT, ipady=8)

        self.create_modern_button(button_frame, "❌ Close", dialog.destroy,
                                 bg=self.colors['bg_input'], width=12).pack(side=tk.RIGHT, ipady=8)

    def copy_password(self, site):
        """Copy password to clipboard."""
        if site in self.passwords:
            password = self.passwords[site].get('password', '')
            self.root.clipboard_clear()
            self.root.clipboard_append(password)
            self.show_notification(f"Password for {site} copied!", "success")
        else:
            self.show_notification("Password not found!", "danger")

    def edit_password(self, site):
        """Edit existing password."""
        if site not in self.passwords:
            self.show_notification("Password not found!", "danger")
            return

        data = self.passwords[site]
        self.show_password_dialog(f"Edit Password - {site}",
                                 site, data.get('username', ''),
                                 data.get('password', ''), data.get('url', ''))

    def delete_password(self, site):
        """Delete password with confirmation."""
        result = messagebox.askyesno("Confirm Delete",
                                   f"Are you sure you want to delete the password for {site}?\n\nThis action cannot be undone.",
                                   icon='warning')

        if result:
            if site in self.passwords:
                del self.passwords[site]
                self.save_data()
                self.refresh_passwords_display()
                self.show_notification(f"Password for {site} deleted!", "success")
            else:
                self.show_notification("Password not found!", "danger")

    def logout(self):
        """Logout and return to login screen."""
        self.passwords = {}
        self.master_password = None
        self.create_login_screen()
        self.show_notification("Logged out successfully!", "success")

    def run(self):
        """Run the application."""
        # Set window icon
        try:
            self.root.iconbitmap(default='')
        except:
            pass

        # Center window on screen
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1000 // 2)
        y = (self.root.winfo_screenheight() // 2) - (700 // 2)
        self.root.geometry(f"1000x700+{x}+{y}")

        # Start the application
        self.root.mainloop()

if __name__ == "__main__":
    app = ModernPasswordVault()
    app.run()
