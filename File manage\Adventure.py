import time
import sys
import random

inventory = []

def slow_type(text, delay=0.02):
    for char in text:
        sys.stdout.write(char)
        sys.stdout.flush()
        time.sleep(delay)
    print()

def intro():
    slow_type("You awaken in the Ultimate Mega Giga Cave™️.")
    slow_type("You have NOTHING on you except your wits (which are questionable).")
    slow_type("There are 3 paths in front of you:")
    slow_type("1. LEFT - It's dark and smells like burnt hair.")
    slow_type("2. CENTER - You hear music. Weird.")
    slow_type("3. RIGHT - A cold breeze. Spooky.")
    choice = input("Choose your path (left/center/right): ").lower()

    if choice == "left":
        dragon_lair()
    elif choice == "center":
        goblin_party()
    elif choice == "right":
        icy_passage()
    else:
        slow_type("You stand there drooling until you starve. Good job.")
        play_again()

def dragon_lair():
    slow_type("\nYou creep left and enter a massive cavern...")
    slow_type("A DRAGON sleeps atop a mountain of loot. 💰🔥")
    if "sword" in inventory:
        slow_type("You clutch your sword tightly.")
    choice = input("Do you SNEAK, FIGHT, or RUN? ").lower()

    if choice == "sneak":
        if random.random() < 0.5:
            slow_type("You trip over a gold coin. The dragon wakes up and fricassees you.")
            play_again()
        else:
            slow_type("You grab some gold and escape! You're rich!")
            inventory.append("gold")
            crossroad()
    elif choice == "fight":
        if "sword" in inventory:
            slow_type("You heroically slay the dragon with your sword! Epic!")
            inventory.append("dragon head")
            crossroad()
        else:
            slow_type("You punch the dragon. He is unimpressed. You are now extra crispy.")
            play_again()
    elif choice == "run":
        slow_type("You run screaming back like a toddler. Nice.")
        intro()
    else:
        slow_type("You stand there pondering life. The dragon eats you like popcorn.")
        play_again()

def goblin_party():
    slow_type("\nYou walk into the center path and hear rave music.")
    slow_type("You find a rave full of goblins. They offer you drinks. 🥳")
    choice = input("Do you DRINK, DANCE, or LEAVE? ").lower()

    if choice == "drink":
        slow_type("You get absolutely wasted. You wake up with a goblin tattoo and a sword!")
        inventory.append("sword")
        crossroad()
    elif choice == "dance":
        slow_type("You dance so well the goblins give you gold.")
        inventory.append("gold")
        crossroad()
    elif choice == "leave":
        slow_type("You back away slowly. They boo you.")
        crossroad()
    else:
        slow_type("You vibe too hard and pass out. The goblins draw on your face with sharpie.")
        play_again()

def icy_passage():
    slow_type("\nYou shiver as you enter the right passage.")
    slow_type("You see a giant YETI eating ice cream.")
    choice = input("Do you TALK, ATTACK, or RUN? ").lower()

    if choice == "talk":
        slow_type("The Yeti is lonely. You bond over memes. He gives you an ice amulet.")
        inventory.append("ice amulet")
        crossroad()
    elif choice == "attack":
        slow_type("You swing at the Yeti. Bad idea. He yeets you into space.")
        play_again()
    elif choice == "run":
        slow_type("You run back to the crossroads like a wuss.")
        intro()
    else:
        slow_type("You slip on ice and die of embarrassment.")
        play_again()

def crossroad():
    slow_type("\nYou find yourself at a new crossroads.")
    slow_type(f"Your inventory: {inventory}")
    slow_type("Paths ahead:")
    slow_type("1. FOREST - Sounds of birds.")
    slow_type("2. CAVE - Dripping water.")
    slow_type("3. CASTLE - Looks ominous.")
    choice = input("Choose your path (forest/cave/castle): ").lower()

    if choice == "forest":
        enchanted_forest()
    elif choice == "cave":
        dark_cave()
    elif choice == "castle":
        spooky_castle()
    else:
        slow_type("You wander in circles and die of confusion.")
        play_again()

def enchanted_forest():
    slow_type("\nYou enter a glowing forest. Fireflies everywhere.")
    if "ice amulet" in inventory:
        slow_type("Your ice amulet glows and freezes a hidden monster!")
        slow_type("You find treasure behind it. You're ballin'!")
        inventory.append("mystic treasure")
    else:
        slow_type("A monster jumps out and eats you like a protein bar.")
        play_again()
    play_again()

def dark_cave():
    slow_type("\nYou venture into the cave...")
    if "torch" in inventory:
        slow_type("Your torch lights the way. You find an ancient crown!")
        inventory.append("crown")
        play_again()
    else:
        slow_type("It's too dark. You fall into a pit and become fossil fuel.")
        play_again()

def spooky_castle():
    slow_type("\nYou approach the castle. Thunder crashes. ⚡️")
    choice = input("Do you ENTER or LEAVE? ").lower()

    if choice == "enter":
        if "gold" in inventory:
            slow_type("You bribe the guards with gold. They let you in.")
            slow_type("Inside, you find a comfy throne. You are now the ruler! 👑")
        else:
            slow_type("The guards laugh at your broke self and toss you in the moat. With crocodiles.")
        play_again()
    elif choice == "leave":
        slow_type("You wander off into the sunset. Lame but alive.")
        play_again()
    else:
        slow_type("You get struck by lightning for being indecisive. Zeus says hi.")
        play_again()

def play_again():
    choice = input("\nPlay again? (yes/no): ").lower()
    if choice == "yes":
        global inventory
        inventory = []
        intro()
    else:
        slow_type("Peace out adventurer. Don't die irl. ✌️")

intro()
