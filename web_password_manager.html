<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 SrujanVault Web</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .login-screen {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .login-card {
            background: rgba(15, 52, 96, 0.9);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            text-align: center;
            min-width: 400px;
        }
        
        .main-interface {
            display: none;
        }
        
        .header {
            background: rgba(15, 52, 96, 0.9);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            backdrop-filter: blur(10px);
        }
        
        .title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .subtitle {
            color: #a0a0a0;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: none;
            border-radius: 10px;
            background: rgba(26, 26, 46, 0.8);
            color: white;
            font-size: 14px;
            outline: none;
            transition: all 0.3s ease;
        }
        
        .form-input:focus {
            background: rgba(26, 26, 46, 1);
            box-shadow: 0 0 0 2px #667eea;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        .btn-success {
            background: linear-gradient(45deg, #00d4aa, #00b894);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #fb5607, #e85d04);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #ffbe0b, #fb8500);
            color: white;
        }
        
        .search-bar {
            background: rgba(15, 52, 96, 0.9);
            padding: 15px;
            border-radius: 15px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        
        .passwords-container {
            background: rgba(15, 52, 96, 0.9);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            max-height: 500px;
            overflow-y: auto;
        }
        
        .password-card {
            background: rgba(26, 26, 46, 0.8);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }
        
        .password-card:hover {
            background: rgba(26, 26, 46, 1);
            transform: translateY(-2px);
        }
        
        .password-info h3 {
            margin-bottom: 5px;
            color: #667eea;
        }
        
        .password-info p {
            color: #a0a0a0;
            font-size: 14px;
        }
        
        .password-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn-small {
            padding: 8px 12px;
            font-size: 12px;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }
        
        .modal-content {
            background: rgba(15, 52, 96, 0.95);
            margin: 5% auto;
            padding: 30px;
            border-radius: 20px;
            width: 90%;
            max-width: 500px;
            backdrop-filter: blur(10px);
        }
        
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #a0a0a0;
        }
        
        .empty-state h3 {
            margin-bottom: 10px;
            font-size: 24px;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-out;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 10px;
            color: white;
            font-weight: bold;
            z-index: 1001;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification.success {
            background: linear-gradient(45deg, #00d4aa, #00b894);
        }
        
        .notification.error {
            background: linear-gradient(45deg, #fb5607, #e85d04);
        }
        
        .notification.warning {
            background: linear-gradient(45deg, #ffbe0b, #fb8500);
        }
    </style>
</head>
<body>
    <!-- Login Screen -->
    <div id="loginScreen" class="login-screen">
        <div class="login-card fade-in">
            <div class="title">🔐 SrujanVault</div>
            <div class="subtitle">Web Password Manager</div>
            
            <div class="form-group">
                <label for="masterPassword">Master Password</label>
                <input type="password" id="masterPassword" class="form-input" placeholder="Enter your master password">
            </div>
            
            <button class="btn btn-primary" onclick="login()">🔓 Unlock Vault</button>
            <button class="btn btn-success" onclick="createNewVault()">✨ Create New Vault</button>
        </div>
    </div>
    
    <!-- Main Interface -->
    <div id="mainInterface" class="main-interface">
        <div class="container">
            <!-- Header -->
            <div class="header fade-in">
                <div>
                    <div class="title">🔐 SrujanVault</div>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="showAddModal()">➕ Add Password</button>
                    <button class="btn btn-success" onclick="showGenerateModal()">🎲 Generate</button>
                    <button class="btn btn-danger" onclick="logout()">🚪 Logout</button>
                </div>
            </div>
            
            <!-- Search Bar -->
            <div class="search-bar fade-in">
                <div class="form-group" style="margin-bottom: 0;">
                    <label for="searchInput">🔍 Search Passwords</label>
                    <input type="text" id="searchInput" class="form-input" placeholder="Search by website or username..." oninput="filterPasswords()">
                </div>
            </div>
            
            <!-- Passwords Container -->
            <div class="passwords-container fade-in">
                <div id="passwordsList">
                    <!-- Passwords will be loaded here -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- Add/Edit Password Modal -->
    <div id="passwordModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('passwordModal')">&times;</span>
            <h2 id="modalTitle">Add New Password</h2>
            
            <div class="form-group">
                <label for="siteInput">Website/Service</label>
                <input type="text" id="siteInput" class="form-input" placeholder="e.g., Gmail, Facebook">
            </div>
            
            <div class="form-group">
                <label for="usernameInput">Username/Email</label>
                <input type="text" id="usernameInput" class="form-input" placeholder="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="passwordInput">Password</label>
                <input type="password" id="passwordInput" class="form-input" placeholder="Enter password">
            </div>
            
            <button class="btn btn-primary" onclick="savePassword()">💾 Save</button>
            <button class="btn btn-danger" onclick="closeModal('passwordModal')">❌ Cancel</button>
        </div>
    </div>
    
    <!-- Password Generator Modal -->
    <div id="generateModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('generateModal')">&times;</span>
            <h2>🎲 Password Generator</h2>
            
            <div class="form-group">
                <label for="lengthSlider">Password Length: <span id="lengthValue">12</span></label>
                <input type="range" id="lengthSlider" min="8" max="32" value="12" oninput="updateLength()" style="width: 100%;">
            </div>
            
            <div class="form-group">
                <label>
                    <input type="checkbox" id="uppercase" checked> Uppercase (A-Z)
                </label><br>
                <label>
                    <input type="checkbox" id="lowercase" checked> Lowercase (a-z)
                </label><br>
                <label>
                    <input type="checkbox" id="numbers" checked> Numbers (0-9)
                </label><br>
                <label>
                    <input type="checkbox" id="symbols" checked> Symbols (!@#$)
                </label>
            </div>
            
            <div class="form-group">
                <label for="generatedPassword">Generated Password</label>
                <input type="text" id="generatedPassword" class="form-input" readonly>
            </div>
            
            <button class="btn btn-primary" onclick="generatePassword()">🎲 Generate</button>
            <button class="btn btn-success" onclick="copyGenerated()">📋 Copy</button>
        </div>
    </div>
    
    <script>
        // Application state
        let passwords = {};
        let masterPassword = '';
        let currentEditingSite = null;
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Check if there's existing data
            const savedData = localStorage.getItem('secureVaultData');
            if (savedData) {
                document.getElementById('masterPassword').placeholder = 'Enter your master password to unlock';
            }
            
            // Enter key bindings
            document.getElementById('masterPassword').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') login();
            });
        });
        
        // Simple hash function
        function simpleHash(str) {
            let hash = 0;
            for (let i = 0; i < str.length; i++) {
                const char = str.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // Convert to 32-bit integer
            }
            return hash.toString();
        }
        
        // Login function
        function login() {
            const password = document.getElementById('masterPassword').value;
            if (!password) {
                showNotification('Please enter your master password!', 'warning');
                return;
            }
            
            const savedData = localStorage.getItem('secureVaultData');
            if (savedData) {
                try {
                    const data = JSON.parse(savedData);
                    if (data.masterHash === simpleHash(password)) {
                        masterPassword = password;
                        passwords = data.passwords || {};
                        showMainInterface();
                        showNotification('Vault unlocked successfully!', 'success');
                    } else {
                        showNotification('Invalid master password!', 'error');
                    }
                } catch (e) {
                    showNotification('Error loading vault data!', 'error');
                }
            } else {
                showNotification('No vault found. Create a new one!', 'warning');
            }
        }
        
        // Create new vault
        function createNewVault() {
            const password = document.getElementById('masterPassword').value;
            if (password.length < 6) {
                showNotification('Master password must be at least 6 characters!', 'warning');
                return;
            }
            
            masterPassword = password;
            passwords = {};
            saveData();
            showMainInterface();
            showNotification('New vault created successfully!', 'success');
        }
        
        // Save data to localStorage
        function saveData() {
            const data = {
                masterHash: simpleHash(masterPassword),
                passwords: passwords
            };
            localStorage.setItem('secureVaultData', JSON.stringify(data));
        }
        
        // Show main interface
        function showMainInterface() {
            document.getElementById('loginScreen').style.display = 'none';
            document.getElementById('mainInterface').style.display = 'block';
            loadPasswords();
        }
        
        // Load and display passwords
        function loadPasswords() {
            const container = document.getElementById('passwordsList');
            container.innerHTML = '';
            
            if (Object.keys(passwords).length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <h3>🔒 No passwords saved yet</h3>
                        <p>Click "Add Password" to get started!</p>
                    </div>
                `;
                return;
            }
            
            for (const [site, data] of Object.entries(passwords)) {
                const card = document.createElement('div');
                card.className = 'password-card';
                card.innerHTML = `
                    <div class="password-info">
                        <h3>🌐 ${site}</h3>
                        <p>👤 ${data.username}</p>
                        <p>🔑 ${'•'.repeat(Math.min(data.password.length, 12))}</p>
                    </div>
                    <div class="password-actions">
                        <button class="btn btn-primary btn-small" onclick="copyPassword('${site}')">📋</button>
                        <button class="btn btn-warning btn-small" onclick="editPassword('${site}')">✏️</button>
                        <button class="btn btn-danger btn-small" onclick="deletePassword('${site}')">🗑️</button>
                    </div>
                `;
                container.appendChild(card);
            }
        }
        
        // Filter passwords
        function filterPasswords() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const cards = document.querySelectorAll('.password-card');
            
            cards.forEach(card => {
                const text = card.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    card.style.display = 'flex';
                } else {
                    card.style.display = 'none';
                }
            });
        }
        
        // Copy password
        function copyPassword(site) {
            if (passwords[site]) {
                navigator.clipboard.writeText(passwords[site].password).then(() => {
                    showNotification(`Password for ${site} copied!`, 'success');
                }).catch(() => {
                    // Fallback for older browsers
                    const textArea = document.createElement('textarea');
                    textArea.value = passwords[site].password;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    showNotification(`Password for ${site} copied!`, 'success');
                });
            }
        }
        
        // Edit password
        function editPassword(site) {
            if (passwords[site]) {
                currentEditingSite = site;
                document.getElementById('modalTitle').textContent = `Edit Password for ${site}`;
                document.getElementById('siteInput').value = site;
                document.getElementById('usernameInput').value = passwords[site].username;
                document.getElementById('passwordInput').value = passwords[site].password;
                document.getElementById('passwordModal').style.display = 'block';
            }
        }
        
        // Delete password
        function deletePassword(site) {
            if (confirm(`Are you sure you want to delete the password for ${site}?`)) {
                delete passwords[site];
                saveData();
                loadPasswords();
                showNotification(`Password for ${site} deleted!`, 'success');
            }
        }
        
        // Show add modal
        function showAddModal() {
            currentEditingSite = null;
            document.getElementById('modalTitle').textContent = 'Add New Password';
            document.getElementById('siteInput').value = '';
            document.getElementById('usernameInput').value = '';
            document.getElementById('passwordInput').value = '';
            document.getElementById('passwordModal').style.display = 'block';
        }
        
        // Save password
        function savePassword() {
            const site = document.getElementById('siteInput').value.trim();
            const username = document.getElementById('usernameInput').value.trim();
            const password = document.getElementById('passwordInput').value.trim();
            
            if (!site || !username || !password) {
                showNotification('Please fill all fields!', 'warning');
                return;
            }
            
            passwords[site] = { username, password };
            saveData();
            loadPasswords();
            closeModal('passwordModal');
            
            const action = currentEditingSite ? 'updated' : 'added';
            showNotification(`Password ${action} successfully!`, 'success');
        }
        
        // Show generate modal
        function showGenerateModal() {
            document.getElementById('generateModal').style.display = 'block';
        }
        
        // Update length display
        function updateLength() {
            const length = document.getElementById('lengthSlider').value;
            document.getElementById('lengthValue').textContent = length;
        }
        
        // Generate password
        function generatePassword() {
            const length = parseInt(document.getElementById('lengthSlider').value);
            const uppercase = document.getElementById('uppercase').checked;
            const lowercase = document.getElementById('lowercase').checked;
            const numbers = document.getElementById('numbers').checked;
            const symbols = document.getElementById('symbols').checked;
            
            let chars = '';
            if (uppercase) chars += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
            if (lowercase) chars += 'abcdefghijklmnopqrstuvwxyz';
            if (numbers) chars += '0123456789';
            if (symbols) chars += '!@#$%^&*';
            
            if (!chars) {
                showNotification('Please select at least one character type!', 'warning');
                return;
            }
            
            let password = '';
            for (let i = 0; i < length; i++) {
                password += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            
            document.getElementById('generatedPassword').value = password;
        }
        
        // Copy generated password
        function copyGenerated() {
            const password = document.getElementById('generatedPassword').value;
            if (password) {
                navigator.clipboard.writeText(password).then(() => {
                    showNotification('Generated password copied!', 'success');
                }).catch(() => {
                    // Fallback
                    document.getElementById('generatedPassword').select();
                    document.execCommand('copy');
                    showNotification('Generated password copied!', 'success');
                });
            }
        }
        
        // Close modal
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }
        
        // Logout
        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                passwords = {};
                masterPassword = '';
                document.getElementById('mainInterface').style.display = 'none';
                document.getElementById('loginScreen').style.display = 'flex';
                document.getElementById('masterPassword').value = '';
            }
        }
        
        // Show notification
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => notification.classList.add('show'), 100);
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 3000);
        }
        
        // Close modals when clicking outside
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html>
