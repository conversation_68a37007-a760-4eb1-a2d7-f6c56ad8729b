#!/usr/bin/env python3
"""
🌟 EPIC ADVENTURE GAME 🌟
A massive adventure game with multiple worlds, complex storylines, and endless possibilities!
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import json
import random
import time
import os
from datetime import datetime

class EpicAdventureGame:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🌟 Epic Adventure - The Ultimate Quest")
        self.root.geometry("1200x800")
        self.root.configure(bg='#0a0a0a')
        
        # Game state
        self.player = {
            'name': '',
            'level': 1,
            'xp': 0,
            'xp_to_next': 100,
            'hp': 100,
            'max_hp': 100,
            'mp': 50,
            'max_mp': 50,
            'gold': 100,
            'strength': 10,
            'defense': 10,
            'magic': 10,
            'agility': 10,
            'inventory': [],
            'equipment': {'weapon': None, 'armor': None, 'accessory': None},
            'current_world': 'tutorial',
            'current_location': 'village_square',
            'story_flags': {},
            'achievements': [],
            'skills': {},
            'companions': [],
            'quests': {'active': [], 'completed': []},
            'playtime': 0,
            'deaths': 0
        }
        
        # Color scheme
        self.colors = {
            'bg_primary': '#0a0a0a',
            'bg_secondary': '#1a1a2e',
            'bg_card': '#16213e',
            'accent': '#e94560',
            'text_primary': '#ffffff',
            'text_secondary': '#a0a0a0',
            'success': '#00d4aa',
            'warning': '#ffbe0b',
            'danger': '#fb5607',
            'magic': '#9d4edd',
            'rare': '#f77f00',
            'legendary': '#fcbf49'
        }
        
        # Game data
        self.worlds = self.initialize_worlds()
        self.items = self.initialize_items()
        self.monsters = self.initialize_monsters()
        self.npcs = self.initialize_npcs()
        self.quests_data = self.initialize_quests()
        self.skills_data = self.initialize_skills()
        
        # UI Components
        self.setup_ui()
        self.show_main_menu()
    
    def initialize_worlds(self):
        """Initialize all game worlds and locations."""
        return {
            'tutorial': {
                'name': '🏘️ Peaceful Village',
                'description': 'A quiet village where your adventure begins.',
                'locations': {
                    'village_square': {
                        'name': 'Village Square',
                        'description': 'The heart of the village with a beautiful fountain.',
                        'exits': ['inn', 'shop', 'forest_entrance'],
                        'npcs': ['village_elder', 'merchant'],
                        'items': ['health_potion'],
                        'events': ['tutorial_start']
                    },
                    'inn': {
                        'name': 'The Cozy Inn',
                        'description': 'A warm place to rest and recover.',
                        'exits': ['village_square'],
                        'npcs': ['innkeeper'],
                        'services': ['rest', 'save_game']
                    },
                    'shop': {
                        'name': 'General Store',
                        'description': 'Buy and sell items here.',
                        'exits': ['village_square'],
                        'npcs': ['shopkeeper'],
                        'services': ['buy', 'sell']
                    },
                    'forest_entrance': {
                        'name': 'Forest Entrance',
                        'description': 'The edge of a mysterious forest.',
                        'exits': ['village_square', 'dark_forest'],
                        'monsters': ['forest_wolf'],
                        'items': ['wooden_sword']
                    }
                }
            },
            'dark_forest': {
                'name': '🌲 Dark Forest',
                'description': 'A dangerous forest filled with monsters and secrets.',
                'locations': {
                    'forest_path': {
                        'name': 'Forest Path',
                        'description': 'A winding path through tall trees.',
                        'exits': ['forest_clearing', 'deep_woods'],
                        'monsters': ['forest_wolf', 'giant_spider'],
                        'items': ['herb', 'magic_crystal']
                    },
                    'forest_clearing': {
                        'name': 'Forest Clearing',
                        'description': 'A peaceful clearing with ancient ruins.',
                        'exits': ['forest_path', 'ancient_ruins'],
                        'npcs': ['forest_hermit'],
                        'items': ['ancient_scroll']
                    },
                    'deep_woods': {
                        'name': 'Deep Woods',
                        'description': 'The darkest part of the forest.',
                        'exits': ['forest_path', 'dragon_lair'],
                        'monsters': ['shadow_beast', 'forest_troll'],
                        'items': ['rare_gem', 'enchanted_bow']
                    },
                    'ancient_ruins': {
                        'name': 'Ancient Ruins',
                        'description': 'Mysterious ruins with magical energy.',
                        'exits': ['forest_clearing'],
                        'monsters': ['stone_guardian'],
                        'items': ['magic_staff', 'ancient_tome'],
                        'events': ['magic_awakening']
                    },
                    'dragon_lair': {
                        'name': 'Dragon\'s Lair',
                        'description': 'A massive cave where an ancient dragon sleeps.',
                        'exits': ['deep_woods'],
                        'monsters': ['ancient_dragon'],
                        'items': ['dragon_treasure', 'legendary_sword'],
                        'events': ['dragon_encounter']
                    }
                }
            },
            'mountain_peaks': {
                'name': '⛰️ Frozen Peaks',
                'description': 'Treacherous mountains with hidden secrets.',
                'locations': {
                    'mountain_base': {
                        'name': 'Mountain Base',
                        'description': 'The beginning of your mountain climb.',
                        'exits': ['snowy_path', 'ice_cave'],
                        'monsters': ['ice_wolf', 'frost_giant'],
                        'items': ['warm_cloak', 'ice_crystal']
                    },
                    'snowy_path': {
                        'name': 'Snowy Path',
                        'description': 'A dangerous path up the mountain.',
                        'exits': ['mountain_base', 'peak_summit'],
                        'monsters': ['yeti', 'ice_elemental'],
                        'items': ['frost_blade', 'mountain_herb']
                    },
                    'ice_cave': {
                        'name': 'Ice Cave',
                        'description': 'A cave filled with magical ice.',
                        'exits': ['mountain_base', 'crystal_chamber'],
                        'monsters': ['ice_golem'],
                        'items': ['ice_armor', 'frozen_orb']
                    },
                    'crystal_chamber': {
                        'name': 'Crystal Chamber',
                        'description': 'A chamber filled with magical crystals.',
                        'exits': ['ice_cave'],
                        'monsters': ['crystal_guardian'],
                        'items': ['power_crystal', 'crystal_sword'],
                        'events': ['crystal_awakening']
                    },
                    'peak_summit': {
                        'name': 'Peak Summit',
                        'description': 'The highest point of the mountain.',
                        'exits': ['snowy_path'],
                        'monsters': ['mountain_dragon'],
                        'items': ['summit_treasure', 'wind_blade'],
                        'events': ['mountain_mastery']
                    }
                }
            },
            'desert_wastes': {
                'name': '🏜️ Desert Wastes',
                'description': 'A vast desert with ancient mysteries.',
                'locations': {
                    'desert_oasis': {
                        'name': 'Desert Oasis',
                        'description': 'A life-saving oasis in the desert.',
                        'exits': ['sand_dunes', 'ancient_city'],
                        'npcs': ['desert_trader'],
                        'services': ['rest', 'trade']
                    },
                    'sand_dunes': {
                        'name': 'Sand Dunes',
                        'description': 'Endless dunes under the scorching sun.',
                        'exits': ['desert_oasis', 'buried_temple'],
                        'monsters': ['sand_worm', 'desert_bandit'],
                        'items': ['desert_gem', 'sun_crystal']
                    },
                    'ancient_city': {
                        'name': 'Ancient City',
                        'description': 'Ruins of a once-great civilization.',
                        'exits': ['desert_oasis', 'pharaoh_tomb'],
                        'monsters': ['mummy', 'sand_elemental'],
                        'items': ['ancient_gold', 'pharaoh_staff']
                    },
                    'buried_temple': {
                        'name': 'Buried Temple',
                        'description': 'A temple buried in sand for millennia.',
                        'exits': ['sand_dunes'],
                        'monsters': ['temple_guardian', 'cursed_priest'],
                        'items': ['holy_relic', 'blessed_armor'],
                        'events': ['temple_blessing']
                    },
                    'pharaoh_tomb': {
                        'name': 'Pharaoh\'s Tomb',
                        'description': 'The final resting place of an ancient pharaoh.',
                        'exits': ['ancient_city'],
                        'monsters': ['pharaoh_spirit', 'tomb_guardian'],
                        'items': ['pharaoh_crown', 'eternal_flame'],
                        'events': ['pharaoh_curse']
                    }
                }
            },
            'underwater_realm': {
                'name': '🌊 Underwater Realm',
                'description': 'A magical underwater world.',
                'locations': {
                    'coral_reef': {
                        'name': 'Coral Reef',
                        'description': 'A beautiful reef teeming with life.',
                        'exits': ['deep_ocean', 'mermaid_city'],
                        'monsters': ['shark', 'electric_eel'],
                        'items': ['pearl', 'coral_staff']
                    },
                    'deep_ocean': {
                        'name': 'Deep Ocean',
                        'description': 'The mysterious depths of the ocean.',
                        'exits': ['coral_reef', 'abyssal_trench'],
                        'monsters': ['giant_octopus', 'sea_serpent'],
                        'items': ['trident', 'water_crystal']
                    },
                    'mermaid_city': {
                        'name': 'Mermaid City',
                        'description': 'A beautiful underwater city.',
                        'exits': ['coral_reef', 'palace_depths'],
                        'npcs': ['mermaid_queen', 'sea_witch'],
                        'services': ['magic_training', 'underwater_shop']
                    },
                    'abyssal_trench': {
                        'name': 'Abyssal Trench',
                        'description': 'The deepest, darkest part of the ocean.',
                        'exits': ['deep_ocean'],
                        'monsters': ['kraken', 'abyssal_horror'],
                        'items': ['abyssal_gem', 'void_weapon'],
                        'events': ['abyss_awakening']
                    },
                    'palace_depths': {
                        'name': 'Palace Depths',
                        'description': 'The royal palace of the underwater realm.',
                        'exits': ['mermaid_city'],
                        'monsters': ['corrupted_guardian'],
                        'items': ['royal_crown', 'ocean_heart'],
                        'events': ['underwater_coronation']
                    }
                }
            },
            'sky_realm': {
                'name': '☁️ Sky Realm',
                'description': 'Floating islands high in the clouds.',
                'locations': {
                    'cloud_bridge': {
                        'name': 'Cloud Bridge',
                        'description': 'A bridge made of solid clouds.',
                        'exits': ['floating_island', 'storm_castle'],
                        'monsters': ['wind_elemental', 'sky_pirate'],
                        'items': ['wind_crystal', 'cloud_boots']
                    },
                    'floating_island': {
                        'name': 'Floating Island',
                        'description': 'A peaceful island floating in the sky.',
                        'exits': ['cloud_bridge', 'celestial_garden'],
                        'npcs': ['sky_sage', 'wind_spirit'],
                        'items': ['sky_gem', 'feather_cloak']
                    },
                    'storm_castle': {
                        'name': 'Storm Castle',
                        'description': 'A castle surrounded by eternal storms.',
                        'exits': ['cloud_bridge', 'throne_room'],
                        'monsters': ['storm_knight', 'lightning_beast'],
                        'items': ['storm_blade', 'thunder_armor']
                    },
                    'celestial_garden': {
                        'name': 'Celestial Garden',
                        'description': 'A garden where stars grow like flowers.',
                        'exits': ['floating_island'],
                        'monsters': ['star_guardian'],
                        'items': ['star_fruit', 'celestial_staff'],
                        'events': ['star_blessing']
                    },
                    'throne_room': {
                        'name': 'Sky Throne Room',
                        'description': 'The throne room of the Sky King.',
                        'exits': ['storm_castle'],
                        'monsters': ['sky_king'],
                        'items': ['sky_crown', 'wind_scepter'],
                        'events': ['sky_mastery']
                    }
                }
            },
            'shadow_realm': {
                'name': '🌑 Shadow Realm',
                'description': 'A dark realm where nightmares come alive.',
                'locations': {
                    'shadow_gate': {
                        'name': 'Shadow Gate',
                        'description': 'The entrance to the realm of shadows.',
                        'exits': ['nightmare_forest', 'void_plains'],
                        'monsters': ['shadow_wraith', 'dark_spirit'],
                        'items': ['shadow_cloak', 'void_crystal']
                    },
                    'nightmare_forest': {
                        'name': 'Nightmare Forest',
                        'description': 'A forest where your worst fears take shape.',
                        'exits': ['shadow_gate', 'terror_castle'],
                        'monsters': ['nightmare_beast', 'fear_demon'],
                        'items': ['nightmare_blade', 'terror_gem']
                    },
                    'void_plains': {
                        'name': 'Void Plains',
                        'description': 'Empty plains that drain your very soul.',
                        'exits': ['shadow_gate', 'dark_citadel'],
                        'monsters': ['void_walker', 'soul_eater'],
                        'items': ['void_armor', 'soul_crystal']
                    },
                    'terror_castle': {
                        'name': 'Terror Castle',
                        'description': 'A castle built from pure fear.',
                        'exits': ['nightmare_forest'],
                        'monsters': ['terror_lord', 'fear_knight'],
                        'items': ['fear_crown', 'terror_staff'],
                        'events': ['fear_mastery']
                    },
                    'dark_citadel': {
                        'name': 'Dark Citadel',
                        'description': 'The fortress of the Shadow Lord.',
                        'exits': ['void_plains'],
                        'monsters': ['shadow_lord', 'darkness_incarnate'],
                        'items': ['shadow_crown', 'void_scepter'],
                        'events': ['shadow_mastery']
                    }
                }
            }
        }
    
    def initialize_items(self):
        """Initialize all game items."""
        return {
            # Weapons
            'wooden_sword': {'name': 'Wooden Sword', 'type': 'weapon', 'attack': 5, 'value': 10, 'rarity': 'common'},
            'iron_sword': {'name': 'Iron Sword', 'type': 'weapon', 'attack': 15, 'value': 50, 'rarity': 'common'},
            'steel_sword': {'name': 'Steel Sword', 'type': 'weapon', 'attack': 25, 'value': 150, 'rarity': 'uncommon'},
            'enchanted_bow': {'name': 'Enchanted Bow', 'type': 'weapon', 'attack': 20, 'magic': 10, 'value': 200, 'rarity': 'rare'},
            'magic_staff': {'name': 'Magic Staff', 'type': 'weapon', 'attack': 10, 'magic': 30, 'value': 300, 'rarity': 'rare'},
            'legendary_sword': {'name': 'Legendary Sword', 'type': 'weapon', 'attack': 50, 'value': 1000, 'rarity': 'legendary'},
            'frost_blade': {'name': 'Frost Blade', 'type': 'weapon', 'attack': 30, 'ice_damage': 15, 'value': 400, 'rarity': 'rare'},
            'crystal_sword': {'name': 'Crystal Sword', 'type': 'weapon', 'attack': 35, 'magic': 20, 'value': 600, 'rarity': 'epic'},
            'wind_blade': {'name': 'Wind Blade', 'type': 'weapon', 'attack': 40, 'agility': 10, 'value': 700, 'rarity': 'epic'},
            'pharaoh_staff': {'name': 'Pharaoh Staff', 'type': 'weapon', 'attack': 25, 'magic': 40, 'value': 800, 'rarity': 'epic'},
            'trident': {'name': 'Trident', 'type': 'weapon', 'attack': 45, 'water_damage': 20, 'value': 900, 'rarity': 'epic'},
            'storm_blade': {'name': 'Storm Blade', 'type': 'weapon', 'attack': 50, 'lightning': 25, 'value': 1200, 'rarity': 'legendary'},
            'nightmare_blade': {'name': 'Nightmare Blade', 'type': 'weapon', 'attack': 55, 'dark_damage': 30, 'value': 1500, 'rarity': 'legendary'},
            'void_weapon': {'name': 'Void Weapon', 'type': 'weapon', 'attack': 60, 'void_damage': 35, 'value': 2000, 'rarity': 'mythic'},
            
            # Armor
            'leather_armor': {'name': 'Leather Armor', 'type': 'armor', 'defense': 5, 'value': 25, 'rarity': 'common'},
            'chain_mail': {'name': 'Chain Mail', 'type': 'armor', 'defense': 15, 'value': 100, 'rarity': 'common'},
            'plate_armor': {'name': 'Plate Armor', 'type': 'armor', 'defense': 25, 'value': 300, 'rarity': 'uncommon'},
            'ice_armor': {'name': 'Ice Armor', 'type': 'armor', 'defense': 30, 'ice_resist': 50, 'value': 500, 'rarity': 'rare'},
            'blessed_armor': {'name': 'Blessed Armor', 'type': 'armor', 'defense': 35, 'holy_power': 20, 'value': 700, 'rarity': 'epic'},
            'thunder_armor': {'name': 'Thunder Armor', 'type': 'armor', 'defense': 40, 'lightning_resist': 60, 'value': 900, 'rarity': 'epic'},
            'void_armor': {'name': 'Void Armor', 'type': 'armor', 'defense': 50, 'void_resist': 70, 'value': 1500, 'rarity': 'legendary'},
            
            # Accessories
            'power_ring': {'name': 'Power Ring', 'type': 'accessory', 'strength': 5, 'value': 100, 'rarity': 'uncommon'},
            'magic_amulet': {'name': 'Magic Amulet', 'type': 'accessory', 'magic': 10, 'value': 200, 'rarity': 'rare'},
            'agility_boots': {'name': 'Agility Boots', 'type': 'accessory', 'agility': 8, 'value': 150, 'rarity': 'uncommon'},
            'cloud_boots': {'name': 'Cloud Boots', 'type': 'accessory', 'agility': 15, 'flight': True, 'value': 400, 'rarity': 'epic'},
            'feather_cloak': {'name': 'Feather Cloak', 'type': 'accessory', 'defense': 10, 'wind_resist': 40, 'value': 300, 'rarity': 'rare'},
            'shadow_cloak': {'name': 'Shadow Cloak', 'type': 'accessory', 'stealth': 20, 'dark_resist': 50, 'value': 600, 'rarity': 'epic'},
            
            # Consumables
            'health_potion': {'name': 'Health Potion', 'type': 'consumable', 'heal': 50, 'value': 20, 'rarity': 'common'},
            'mana_potion': {'name': 'Mana Potion', 'type': 'consumable', 'mana': 30, 'value': 25, 'rarity': 'common'},
            'super_potion': {'name': 'Super Potion', 'type': 'consumable', 'heal': 100, 'mana': 50, 'value': 75, 'rarity': 'uncommon'},
            'elixir': {'name': 'Elixir', 'type': 'consumable', 'full_heal': True, 'value': 200, 'rarity': 'rare'},
            'herb': {'name': 'Healing Herb', 'type': 'consumable', 'heal': 25, 'value': 10, 'rarity': 'common'},
            'mountain_herb': {'name': 'Mountain Herb', 'type': 'consumable', 'heal': 40, 'cold_resist': 10, 'value': 30, 'rarity': 'uncommon'},
            'star_fruit': {'name': 'Star Fruit', 'type': 'consumable', 'heal': 75, 'mana': 75, 'value': 150, 'rarity': 'rare'},
            
            # Special Items
            'magic_crystal': {'name': 'Magic Crystal', 'type': 'special', 'magic_power': 10, 'value': 100, 'rarity': 'uncommon'},
            'rare_gem': {'name': 'Rare Gem', 'type': 'special', 'value': 500, 'rarity': 'rare'},
            'ancient_scroll': {'name': 'Ancient Scroll', 'type': 'special', 'knowledge': True, 'value': 200, 'rarity': 'rare'},
            'ancient_tome': {'name': 'Ancient Tome', 'type': 'special', 'spell_book': True, 'value': 400, 'rarity': 'epic'},
            'dragon_treasure': {'name': 'Dragon Treasure', 'type': 'special', 'value': 2000, 'rarity': 'legendary'},
            'ice_crystal': {'name': 'Ice Crystal', 'type': 'special', 'ice_power': 15, 'value': 150, 'rarity': 'uncommon'},
            'sun_crystal': {'name': 'Sun Crystal', 'type': 'special', 'fire_power': 20, 'value': 200, 'rarity': 'rare'},
            'water_crystal': {'name': 'Water Crystal', 'type': 'special', 'water_power': 18, 'value': 180, 'rarity': 'rare'},
            'wind_crystal': {'name': 'Wind Crystal', 'type': 'special', 'wind_power': 22, 'value': 220, 'rarity': 'rare'},
            'void_crystal': {'name': 'Void Crystal', 'type': 'special', 'void_power': 30, 'value': 500, 'rarity': 'epic'},
            'soul_crystal': {'name': 'Soul Crystal', 'type': 'special', 'soul_power': 25, 'value': 400, 'rarity': 'epic'},
            'pearl': {'name': 'Ocean Pearl', 'type': 'special', 'water_breathing': True, 'value': 300, 'rarity': 'rare'},
            'holy_relic': {'name': 'Holy Relic', 'type': 'special', 'holy_power': 40, 'value': 800, 'rarity': 'epic'},
            'eternal_flame': {'name': 'Eternal Flame', 'type': 'special', 'fire_immunity': True, 'value': 1000, 'rarity': 'legendary'},
            'ocean_heart': {'name': 'Ocean Heart', 'type': 'special', 'water_mastery': True, 'value': 1200, 'rarity': 'legendary'},
            'sky_crown': {'name': 'Sky Crown', 'type': 'special', 'wind_mastery': True, 'value': 1500, 'rarity': 'legendary'},
            'shadow_crown': {'name': 'Shadow Crown', 'type': 'special', 'shadow_mastery': True, 'value': 2000, 'rarity': 'mythic'}
        }

    def initialize_monsters(self):
        """Initialize all game monsters."""
        return {
            # Tutorial/Forest Monsters
            'forest_wolf': {
                'name': 'Forest Wolf', 'hp': 30, 'attack': 8, 'defense': 3, 'xp': 15, 'gold': 10,
                'drops': ['health_potion'], 'description': 'A fierce wolf with glowing eyes.'
            },
            'giant_spider': {
                'name': 'Giant Spider', 'hp': 25, 'attack': 12, 'defense': 2, 'xp': 20, 'gold': 15,
                'drops': ['herb', 'magic_crystal'], 'description': 'A massive spider with venomous fangs.'
            },
            'shadow_beast': {
                'name': 'Shadow Beast', 'hp': 45, 'attack': 15, 'defense': 5, 'xp': 35, 'gold': 25,
                'drops': ['rare_gem'], 'description': 'A creature made of pure shadow.'
            },
            'forest_troll': {
                'name': 'Forest Troll', 'hp': 80, 'attack': 20, 'defense': 8, 'xp': 60, 'gold': 40,
                'drops': ['iron_sword', 'chain_mail'], 'description': 'A massive troll with incredible strength.'
            },
            'stone_guardian': {
                'name': 'Stone Guardian', 'hp': 100, 'attack': 25, 'defense': 15, 'xp': 80, 'gold': 60,
                'drops': ['magic_staff', 'power_ring'], 'description': 'An ancient guardian made of living stone.'
            },
            'ancient_dragon': {
                'name': 'Ancient Dragon', 'hp': 300, 'attack': 50, 'defense': 25, 'xp': 200, 'gold': 500,
                'drops': ['legendary_sword', 'dragon_treasure'], 'description': 'A mighty dragon with scales like armor.',
                'boss': True
            },

            # Mountain Monsters
            'ice_wolf': {
                'name': 'Ice Wolf', 'hp': 40, 'attack': 12, 'defense': 6, 'xp': 25, 'gold': 20,
                'drops': ['ice_crystal'], 'description': 'A wolf adapted to the frozen peaks.'
            },
            'frost_giant': {
                'name': 'Frost Giant', 'hp': 120, 'attack': 30, 'defense': 12, 'xp': 90, 'gold': 70,
                'drops': ['frost_blade', 'ice_armor'], 'description': 'A giant made of ice and snow.'
            },
            'yeti': {
                'name': 'Yeti', 'hp': 90, 'attack': 25, 'defense': 10, 'xp': 70, 'gold': 50,
                'drops': ['warm_cloak'], 'description': 'A legendary creature of the mountains.'
            },
            'ice_elemental': {
                'name': 'Ice Elemental', 'hp': 60, 'attack': 18, 'defense': 8, 'xp': 45, 'gold': 35,
                'drops': ['ice_crystal', 'mana_potion'], 'description': 'A being of pure ice magic.'
            },
            'mountain_dragon': {
                'name': 'Mountain Dragon', 'hp': 400, 'attack': 60, 'defense': 30, 'xp': 300, 'gold': 800,
                'drops': ['wind_blade', 'summit_treasure'], 'description': 'A dragon that rules the mountain peaks.',
                'boss': True
            },

            # Desert Monsters
            'sand_worm': {
                'name': 'Sand Worm', 'hp': 70, 'attack': 22, 'defense': 5, 'xp': 50, 'gold': 40,
                'drops': ['desert_gem'], 'description': 'A massive worm that burrows through sand.'
            },
            'desert_bandit': {
                'name': 'Desert Bandit', 'hp': 50, 'attack': 18, 'defense': 8, 'xp': 35, 'gold': 60,
                'drops': ['steel_sword', 'ancient_gold'], 'description': 'A ruthless bandit of the desert.'
            },
            'mummy': {
                'name': 'Mummy', 'hp': 80, 'attack': 20, 'defense': 12, 'xp': 60, 'gold': 45,
                'drops': ['ancient_scroll'], 'description': 'An undead guardian wrapped in ancient cloth.'
            },
            'pharaoh_spirit': {
                'name': 'Pharaoh Spirit', 'hp': 250, 'attack': 45, 'defense': 20, 'xp': 200, 'gold': 300,
                'drops': ['pharaoh_crown'], 'description': 'The vengeful spirit of an ancient pharaoh.',
                'boss': True
            },

            # Underwater Monsters
            'shark': {
                'name': 'Great Shark', 'hp': 60, 'attack': 20, 'defense': 8, 'xp': 40, 'gold': 30,
                'drops': ['pearl'], 'description': 'A massive predator of the deep.'
            },
            'giant_octopus': {
                'name': 'Giant Octopus', 'hp': 100, 'attack': 30, 'defense': 12, 'xp': 80, 'gold': 60,
                'drops': ['trident'], 'description': 'A massive octopus with crushing tentacles.'
            },
            'kraken': {
                'name': 'Kraken', 'hp': 300, 'attack': 50, 'defense': 20, 'xp': 250, 'gold': 400,
                'drops': ['ocean_heart'], 'description': 'The legendary terror of the deep ocean.',
                'boss': True
            },

            # Sky Realm Monsters
            'wind_elemental': {
                'name': 'Wind Elemental', 'hp': 70, 'attack': 22, 'defense': 6, 'xp': 50, 'gold': 40,
                'drops': ['wind_crystal'], 'description': 'A being of pure wind and storm.'
            },
            'sky_pirate': {
                'name': 'Sky Pirate', 'hp': 80, 'attack': 25, 'defense': 10, 'xp': 60, 'gold': 80,
                'drops': ['cloud_boots'], 'description': 'A pirate who sails the clouds.'
            },
            'sky_king': {
                'name': 'Sky King', 'hp': 350, 'attack': 55, 'defense': 28, 'xp': 300, 'gold': 600,
                'drops': ['sky_crown', 'wind_scepter'], 'description': 'The ruler of the sky realm.',
                'boss': True
            },

            # Shadow Realm Monsters
            'shadow_wraith': {
                'name': 'Shadow Wraith', 'hp': 80, 'attack': 28, 'defense': 5, 'xp': 60, 'gold': 50,
                'drops': ['void_crystal'], 'description': 'A wraith born from pure darkness.'
            },
            'nightmare_beast': {
                'name': 'Nightmare Beast', 'hp': 100, 'attack': 32, 'defense': 12, 'xp': 85, 'gold': 70,
                'drops': ['nightmare_blade'], 'description': 'A beast that feeds on fear and nightmares.'
            },
            'shadow_lord': {
                'name': 'Shadow Lord', 'hp': 400, 'attack': 60, 'defense': 30, 'xp': 350, 'gold': 800,
                'drops': ['shadow_crown'], 'description': 'The ultimate ruler of the shadow realm.',
                'boss': True
            },
            'darkness_incarnate': {
                'name': 'Darkness Incarnate', 'hp': 500, 'attack': 70, 'defense': 35, 'xp': 500, 'gold': 1000,
                'drops': ['void_scepter'], 'description': 'The physical manifestation of darkness itself.',
                'boss': True, 'final_boss': True
            }
        }

    def initialize_npcs(self):
        """Initialize all NPCs."""
        return {
            'village_elder': {
                'name': 'Village Elder', 'dialogue': [
                    "Welcome, young adventurer! Our village needs your help.",
                    "The ancient evil stirs in the shadow realm. You must grow strong!",
                    "Seek the masters of each realm to gain their power."
                ], 'quests': ['tutorial_quest', 'shadow_prophecy']
            },
            'merchant': {
                'name': 'Traveling Merchant', 'dialogue': [
                    "I have wares from across all the realms!",
                    "Gold for goods, that's my motto!",
                    "Come back when you have more coin!"
                ], 'shop': True
            },
            'innkeeper': {
                'name': 'Innkeeper', 'dialogue': [
                    "Rest here to recover your strength.",
                    "I've heard tales of great treasures in distant lands.",
                    "A good night's sleep works wonders!"
                ], 'services': ['rest']
            },
            'shopkeeper': {
                'name': 'Shopkeeper', 'dialogue': [
                    "Welcome to my shop! What can I get you?",
                    "I buy and sell all kinds of equipment.",
                    "Quality goods at fair prices!"
                ], 'shop': True
            },
            'forest_hermit': {
                'name': 'Forest Hermit', 'dialogue': [
                    "The forest speaks to those who listen.",
                    "Ancient magic flows through these woods.",
                    "Beware the dragon's lair, young one."
                ], 'teaches': ['nature_magic']
            },
            'desert_trader': {
                'name': 'Desert Trader', 'dialogue': [
                    "The desert holds many secrets.",
                    "I trade in rare gems and ancient artifacts.",
                    "Water is more precious than gold here."
                ], 'shop': True, 'special_items': True
            },
            'mermaid_queen': {
                'name': 'Mermaid Queen', 'dialogue': [
                    "Welcome to our underwater kingdom.",
                    "The ocean's power can be yours to command.",
                    "Prove yourself worthy of the sea's blessing."
                ], 'teaches': ['water_magic'], 'quests': ['ocean_mastery']
            },
            'sky_sage': {
                'name': 'Sky Sage', 'dialogue': [
                    "The winds carry ancient wisdom.",
                    "To master the sky, you must become one with the wind.",
                    "The storm king awaits a worthy challenger."
                ], 'teaches': ['wind_magic'], 'quests': ['sky_mastery']
            }
        }

    def initialize_quests(self):
        """Initialize all quests."""
        return {
            'tutorial_quest': {
                'name': 'Village Hero',
                'description': 'Help the village by defeating the forest monsters.',
                'objectives': ['Defeat 3 Forest Wolves', 'Find the Ancient Scroll'],
                'rewards': {'xp': 100, 'gold': 50, 'items': ['iron_sword']},
                'completed': False
            },
            'shadow_prophecy': {
                'name': 'The Shadow Prophecy',
                'description': 'Uncover the ancient prophecy about the shadow realm.',
                'objectives': ['Visit all 7 realms', 'Defeat each realm boss', 'Collect realm artifacts'],
                'rewards': {'xp': 1000, 'gold': 500, 'items': ['legendary_armor']},
                'completed': False
            },
            'dragon_slayer': {
                'name': 'Dragon Slayer',
                'description': 'Defeat the Ancient Dragon in the forest.',
                'objectives': ['Defeat Ancient Dragon'],
                'rewards': {'xp': 300, 'gold': 200, 'items': ['dragon_scale_armor']},
                'completed': False
            },
            'mountain_mastery': {
                'name': 'Master of the Peaks',
                'description': 'Conquer the frozen mountain peaks.',
                'objectives': ['Reach the summit', 'Defeat Mountain Dragon'],
                'rewards': {'xp': 400, 'gold': 300, 'items': ['mountain_crown']},
                'completed': False
            },
            'desert_mysteries': {
                'name': 'Desert Mysteries',
                'description': 'Uncover the secrets of the ancient desert.',
                'objectives': ['Explore all desert locations', 'Defeat Pharaoh Spirit'],
                'rewards': {'xp': 350, 'gold': 250, 'items': ['desert_amulet']},
                'completed': False
            },
            'ocean_mastery': {
                'name': 'Lord of the Seas',
                'description': 'Become the master of the underwater realm.',
                'objectives': ['Defeat the Kraken', 'Gain Mermaid Queen\'s blessing'],
                'rewards': {'xp': 450, 'gold': 400, 'items': ['trident_of_power']},
                'completed': False
            },
            'sky_mastery': {
                'name': 'Sky Walker',
                'description': 'Rule the skies and clouds above.',
                'objectives': ['Defeat Sky King', 'Master wind magic'],
                'rewards': {'xp': 500, 'gold': 450, 'items': ['wings_of_freedom']},
                'completed': False
            },
            'shadow_mastery': {
                'name': 'Shadow Master',
                'description': 'Conquer the realm of darkness and nightmares.',
                'objectives': ['Defeat Shadow Lord', 'Defeat Darkness Incarnate'],
                'rewards': {'xp': 1000, 'gold': 1000, 'items': ['crown_of_shadows']},
                'completed': False
            }
        }

    def initialize_skills(self):
        """Initialize all skills."""
        return {
            'combat_mastery': {
                'name': 'Combat Mastery',
                'description': 'Increases attack power and combat effectiveness.',
                'max_level': 10,
                'effects': {'attack_bonus': 2, 'crit_chance': 1}
            },
            'defense_mastery': {
                'name': 'Defense Mastery',
                'description': 'Increases defense and damage resistance.',
                'max_level': 10,
                'effects': {'defense_bonus': 2, 'damage_reduction': 1}
            },
            'magic_mastery': {
                'name': 'Magic Mastery',
                'description': 'Increases magical power and mana.',
                'max_level': 10,
                'effects': {'magic_bonus': 3, 'mana_bonus': 5}
            },
            'nature_magic': {
                'name': 'Nature Magic',
                'description': 'Harness the power of nature.',
                'max_level': 5,
                'effects': {'nature_damage': 5, 'healing_bonus': 3}
            },
            'fire_magic': {
                'name': 'Fire Magic',
                'description': 'Control the flames of destruction.',
                'max_level': 5,
                'effects': {'fire_damage': 8, 'burn_chance': 10}
            },
            'ice_magic': {
                'name': 'Ice Magic',
                'description': 'Freeze your enemies with ice magic.',
                'max_level': 5,
                'effects': {'ice_damage': 6, 'freeze_chance': 15}
            },
            'water_magic': {
                'name': 'Water Magic',
                'description': 'Command the power of the seas.',
                'max_level': 5,
                'effects': {'water_damage': 7, 'healing_bonus': 5}
            },
            'wind_magic': {
                'name': 'Wind Magic',
                'description': 'Soar with the power of wind.',
                'max_level': 5,
                'effects': {'wind_damage': 6, 'agility_bonus': 3}
            },
            'shadow_magic': {
                'name': 'Shadow Magic',
                'description': 'Embrace the darkness within.',
                'max_level': 5,
                'effects': {'shadow_damage': 10, 'stealth_bonus': 20}
            },
            'holy_magic': {
                'name': 'Holy Magic',
                'description': 'Channel divine power.',
                'max_level': 5,
                'effects': {'holy_damage': 9, 'undead_bonus': 15}
            }
        }

    def setup_ui(self):
        """Setup the main UI components."""
        # Main container
        self.main_frame = tk.Frame(self.root, bg=self.colors['bg_primary'])
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # Create notebook for different screens
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('TNotebook', background=self.colors['bg_primary'])
        style.configure('TNotebook.Tab', background=self.colors['bg_secondary'],
                       foreground=self.colors['text_primary'], padding=[20, 10])

        self.notebook = ttk.Notebook(self.main_frame)

        # Game screen
        self.game_frame = tk.Frame(self.notebook, bg=self.colors['bg_primary'])
        self.notebook.add(self.game_frame, text='🎮 Adventure')

        # Character screen
        self.char_frame = tk.Frame(self.notebook, bg=self.colors['bg_primary'])
        self.notebook.add(self.char_frame, text='👤 Character')

        # Inventory screen
        self.inv_frame = tk.Frame(self.notebook, bg=self.colors['bg_primary'])
        self.notebook.add(self.inv_frame, text='🎒 Inventory')

        # Quests screen
        self.quest_frame = tk.Frame(self.notebook, bg=self.colors['bg_primary'])
        self.notebook.add(self.quest_frame, text='📜 Quests')

        # Skills screen
        self.skills_frame = tk.Frame(self.notebook, bg=self.colors['bg_primary'])
        self.notebook.add(self.skills_frame, text='⚡ Skills')

        # Map screen
        self.map_frame = tk.Frame(self.notebook, bg=self.colors['bg_primary'])
        self.notebook.add(self.map_frame, text='🗺️ Map')

        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Setup individual screens
        self.setup_game_screen()
        self.setup_character_screen()
        self.setup_inventory_screen()
        self.setup_quest_screen()
        self.setup_skills_screen()
        self.setup_map_screen()

    def setup_game_screen(self):
        """Setup the main game screen."""
        # Left panel - Location info
        left_panel = tk.Frame(self.game_frame, bg=self.colors['bg_secondary'], width=400)
        left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)
        left_panel.pack_propagate(False)

        # Location title
        self.location_title = tk.Label(left_panel, text="Location",
                                      font=('Arial', 16, 'bold'),
                                      fg=self.colors['text_primary'],
                                      bg=self.colors['bg_secondary'])
        self.location_title.pack(pady=10)

        # Location description
        self.location_desc = scrolledtext.ScrolledText(left_panel, height=8, width=45,
                                                      bg=self.colors['bg_card'],
                                                      fg=self.colors['text_primary'],
                                                      font=('Arial', 10),
                                                      wrap=tk.WORD)
        self.location_desc.pack(pady=5, padx=10)

        # Action buttons frame
        action_frame = tk.Frame(left_panel, bg=self.colors['bg_secondary'])
        action_frame.pack(pady=10)

        # Movement buttons
        move_frame = tk.LabelFrame(action_frame, text="Movement",
                                  bg=self.colors['bg_secondary'],
                                  fg=self.colors['text_primary'])
        move_frame.pack(pady=5)

        self.move_buttons = {}

        # Interaction buttons
        interact_frame = tk.LabelFrame(action_frame, text="Actions",
                                      bg=self.colors['bg_secondary'],
                                      fg=self.colors['text_primary'])
        interact_frame.pack(pady=5)

        tk.Button(interact_frame, text="🔍 Look Around",
                 command=self.look_around,
                 bg=self.colors['accent'], fg='white').pack(side=tk.LEFT, padx=2)

        tk.Button(interact_frame, text="⚔️ Fight",
                 command=self.start_combat,
                 bg=self.colors['danger'], fg='white').pack(side=tk.LEFT, padx=2)

        tk.Button(interact_frame, text="💬 Talk",
                 command=self.talk_to_npc,
                 bg=self.colors['success'], fg='white').pack(side=tk.LEFT, padx=2)

        # Right panel - Game log and status
        right_panel = tk.Frame(self.game_frame, bg=self.colors['bg_secondary'])
        right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Player status
        status_frame = tk.LabelFrame(right_panel, text="Status",
                                    bg=self.colors['bg_secondary'],
                                    fg=self.colors['text_primary'])
        status_frame.pack(fill=tk.X, pady=5)

        self.status_labels = {}
        status_info = [
            ('name', 'Name'), ('level', 'Level'), ('xp', 'XP'),
            ('hp', 'HP'), ('mp', 'MP'), ('gold', 'Gold')
        ]

        for i, (key, label) in enumerate(status_info):
            row = i // 3
            col = i % 3
            tk.Label(status_frame, text=f"{label}:",
                    bg=self.colors['bg_secondary'],
                    fg=self.colors['text_secondary']).grid(row=row*2, column=col, sticky='w', padx=5)
            self.status_labels[key] = tk.Label(status_frame, text="0",
                                              bg=self.colors['bg_secondary'],
                                              fg=self.colors['text_primary'],
                                              font=('Arial', 10, 'bold'))
            self.status_labels[key].grid(row=row*2+1, column=col, sticky='w', padx=5)

        # Game log
        log_frame = tk.LabelFrame(right_panel, text="Adventure Log",
                                 bg=self.colors['bg_secondary'],
                                 fg=self.colors['text_primary'])
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.game_log = scrolledtext.ScrolledText(log_frame, height=20,
                                                 bg=self.colors['bg_card'],
                                                 fg=self.colors['text_primary'],
                                                 font=('Courier', 9),
                                                 wrap=tk.WORD)
        self.game_log.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def setup_character_screen(self):
        """Setup character information screen."""
        # Character stats
        stats_frame = tk.LabelFrame(self.char_frame, text="Character Stats",
                                   bg=self.colors['bg_secondary'],
                                   fg=self.colors['text_primary'])
        stats_frame.pack(fill=tk.X, padx=10, pady=10)

        self.char_labels = {}
        char_stats = [
            ('name', 'Name'), ('level', 'Level'), ('xp', 'Experience'),
            ('strength', 'Strength'), ('defense', 'Defense'), ('magic', 'Magic'),
            ('agility', 'Agility'), ('hp', 'Health'), ('mp', 'Mana')
        ]

        for i, (key, label) in enumerate(char_stats):
            row = i // 3
            col = i % 3
            tk.Label(stats_frame, text=f"{label}:",
                    bg=self.colors['bg_secondary'],
                    fg=self.colors['text_secondary']).grid(row=row*2, column=col, sticky='w', padx=10, pady=2)
            self.char_labels[key] = tk.Label(stats_frame, text="0",
                                            bg=self.colors['bg_secondary'],
                                            fg=self.colors['text_primary'],
                                            font=('Arial', 12, 'bold'))
            self.char_labels[key].grid(row=row*2+1, column=col, sticky='w', padx=10, pady=2)

        # Equipment
        equip_frame = tk.LabelFrame(self.char_frame, text="Equipment",
                                   bg=self.colors['bg_secondary'],
                                   fg=self.colors['text_primary'])
        equip_frame.pack(fill=tk.X, padx=10, pady=10)

        self.equip_labels = {}
        equipment_slots = [('weapon', 'Weapon'), ('armor', 'Armor'), ('accessory', 'Accessory')]

        for i, (slot, label) in enumerate(equipment_slots):
            tk.Label(equip_frame, text=f"{label}:",
                    bg=self.colors['bg_secondary'],
                    fg=self.colors['text_secondary']).grid(row=i, column=0, sticky='w', padx=10, pady=5)
            self.equip_labels[slot] = tk.Label(equip_frame, text="None",
                                              bg=self.colors['bg_secondary'],
                                              fg=self.colors['text_primary'])
            self.equip_labels[slot].grid(row=i, column=1, sticky='w', padx=10, pady=5)

        # Achievements
        achieve_frame = tk.LabelFrame(self.char_frame, text="Achievements",
                                     bg=self.colors['bg_secondary'],
                                     fg=self.colors['text_primary'])
        achieve_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.achievements_list = tk.Listbox(achieve_frame,
                                           bg=self.colors['bg_card'],
                                           fg=self.colors['text_primary'],
                                           font=('Arial', 10))
        self.achievements_list.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def setup_inventory_screen(self):
        """Setup inventory screen."""
        # Inventory list
        inv_frame = tk.LabelFrame(self.inv_frame, text="Inventory",
                                 bg=self.colors['bg_secondary'],
                                 fg=self.colors['text_primary'])
        inv_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create treeview for inventory
        columns = ('Item', 'Type', 'Quantity', 'Value')
        self.inventory_tree = ttk.Treeview(inv_frame, columns=columns, show='headings')

        for col in columns:
            self.inventory_tree.heading(col, text=col)
            self.inventory_tree.column(col, width=150)

        scrollbar_inv = ttk.Scrollbar(inv_frame, orient=tk.VERTICAL, command=self.inventory_tree.yview)
        self.inventory_tree.configure(yscrollcommand=scrollbar_inv.set)

        self.inventory_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_inv.pack(side=tk.RIGHT, fill=tk.Y)

        # Inventory actions
        inv_actions = tk.Frame(self.inv_frame, bg=self.colors['bg_primary'])
        inv_actions.pack(fill=tk.X, padx=10, pady=5)

        tk.Button(inv_actions, text="Use Item", command=self.use_item,
                 bg=self.colors['success'], fg='white').pack(side=tk.LEFT, padx=5)
        tk.Button(inv_actions, text="Equip", command=self.equip_item,
                 bg=self.colors['accent'], fg='white').pack(side=tk.LEFT, padx=5)
        tk.Button(inv_actions, text="Drop", command=self.drop_item,
                 bg=self.colors['danger'], fg='white').pack(side=tk.LEFT, padx=5)

    def setup_quest_screen(self):
        """Setup quests screen."""
        # Active quests
        active_frame = tk.LabelFrame(self.quest_frame, text="Active Quests",
                                    bg=self.colors['bg_secondary'],
                                    fg=self.colors['text_primary'])
        active_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        self.active_quests = scrolledtext.ScrolledText(active_frame, height=10,
                                                      bg=self.colors['bg_card'],
                                                      fg=self.colors['text_primary'],
                                                      font=('Arial', 10))
        self.active_quests.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Completed quests
        completed_frame = tk.LabelFrame(self.quest_frame, text="Completed Quests",
                                       bg=self.colors['bg_secondary'],
                                       fg=self.colors['text_primary'])
        completed_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        self.completed_quests = scrolledtext.ScrolledText(completed_frame, height=10,
                                                         bg=self.colors['bg_card'],
                                                         fg=self.colors['text_primary'],
                                                         font=('Arial', 10))
        self.completed_quests.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def setup_skills_screen(self):
        """Setup skills screen."""
        # Skills list
        skills_list_frame = tk.LabelFrame(self.skills_frame, text="Skills",
                                         bg=self.colors['bg_secondary'],
                                         fg=self.colors['text_primary'])
        skills_list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create treeview for skills
        skill_columns = ('Skill', 'Level', 'Progress', 'Effect')
        self.skills_tree = ttk.Treeview(skills_list_frame, columns=skill_columns, show='headings')

        for col in skill_columns:
            self.skills_tree.heading(col, text=col)
            self.skills_tree.column(col, width=150)

        scrollbar_skills = ttk.Scrollbar(skills_list_frame, orient=tk.VERTICAL, command=self.skills_tree.yview)
        self.skills_tree.configure(yscrollcommand=scrollbar_skills.set)

        self.skills_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_skills.pack(side=tk.RIGHT, fill=tk.Y)

        # Skill actions
        skill_actions = tk.Frame(self.skills_frame, bg=self.colors['bg_primary'])
        skill_actions.pack(fill=tk.X, padx=10, pady=5)

        tk.Button(skill_actions, text="Train Skill", command=self.train_skill,
                 bg=self.colors['accent'], fg='white').pack(side=tk.LEFT, padx=5)

    def setup_map_screen(self):
        """Setup map screen."""
        # World map
        map_frame = tk.LabelFrame(self.map_frame, text="World Map",
                                 bg=self.colors['bg_secondary'],
                                 fg=self.colors['text_primary'])
        map_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create canvas for map
        self.map_canvas = tk.Canvas(map_frame, bg=self.colors['bg_card'],
                                   width=800, height=600)
        self.map_canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Map legend
        legend_frame = tk.Frame(self.map_frame, bg=self.colors['bg_secondary'])
        legend_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(legend_frame, text="🏘️ Village  🌲 Forest  ⛰️ Mountains  🏜️ Desert  🌊 Ocean  ☁️ Sky  🌑 Shadow",
                bg=self.colors['bg_secondary'], fg=self.colors['text_primary']).pack()

        self.draw_world_map()

    def draw_world_map(self):
        """Draw the world map."""
        # Clear canvas
        self.map_canvas.delete("all")

        # World positions (x, y, world_key, color)
        world_positions = [
            (100, 300, 'tutorial', '#4CAF50'),      # Village (green)
            (250, 250, 'dark_forest', '#2E7D32'),   # Forest (dark green)
            (400, 150, 'mountain_peaks', '#90A4AE'), # Mountains (gray)
            (550, 350, 'desert_wastes', '#FF9800'),  # Desert (orange)
            (300, 450, 'underwater_realm', '#2196F3'), # Ocean (blue)
            (450, 100, 'sky_realm', '#E1F5FE'),      # Sky (light blue)
            (650, 200, 'shadow_realm', '#424242')    # Shadow (dark gray)
        ]

        # Draw connections
        connections = [
            (0, 1), (1, 2), (1, 3), (3, 4), (2, 5), (5, 6), (1, 6)
        ]

        for start, end in connections:
            x1, y1 = world_positions[start][:2]
            x2, y2 = world_positions[end][:2]
            self.map_canvas.create_line(x1+25, y1+25, x2+25, y2+25,
                                       fill='white', width=2)

        # Draw worlds
        for x, y, world_key, color in world_positions:
            world_data = self.worlds[world_key]

            # Draw world circle
            self.map_canvas.create_oval(x, y, x+50, y+50,
                                       fill=color, outline='white', width=2)

            # Add world name
            self.map_canvas.create_text(x+25, y+70, text=world_data['name'],
                                       fill='white', font=('Arial', 8, 'bold'))

            # Highlight current world
            if world_key == self.player['current_world']:
                self.map_canvas.create_oval(x-5, y-5, x+55, y+55,
                                           outline=self.colors['accent'], width=3)
