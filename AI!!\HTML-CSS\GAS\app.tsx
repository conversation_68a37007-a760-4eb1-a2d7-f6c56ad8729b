
import React, { useState, useMemo, useCallback, useEffect } from 'react';

// --- CONSTANTS ---
const ALPHABET = 'abcdefghijklmnopqrstuvwxyz ';
type CipherKey = typeof ALPHABET[number];

export const CIPHER_MAP: Record<CipherKey, string> = {
    'a': 'AB', 'b': 'BBA', 'c': 'BAB', 'd': 'BAA', 'e': 'A',
    'f': 'AAB', 'g': 'BBAA', 'h': 'AAAA', 'i': 'AA', 'j': 'ABBB',
    'k': 'BAB', 'l': 'ABAA', 'm': 'BB', 'n': 'BA', 'o': 'BBB',
    'p': 'ABBA', 'q': 'BABA', 'r': 'ABA', 's': 'AAA', 't': 'B',
    'u': 'AAB', 'v': 'AAAB', 'w': 'ABB', 'x': 'BAAB', 'y': 'BABB',
    'z': 'BBAA', ' ': 'BBBB'
};

// --- CUSTOM HOOK ---
/**
 * A custom hook that creates a typewriter effect for the given text.
 * @param text The text to display with a typewriter effect.
 * @param speed The typing speed in milliseconds. Defaults to 20.
 * @returns The text as it is being "typed".
 */
const useTypewriter = (text: string, speed: number = 20): string => {
  const [displayText, setDisplayText] = useState('');
  
  useEffect(() => {
    setDisplayText('');
    // No need to check for text as an empty string will have length 0
    if (text) {
      let i = 0;
      const textAsString = String(text);
      
      const typingInterval = setInterval(() => {
        // When the end of the string is reached, clear the interval.
        if (i >= textAsString.length) {
          clearInterval(typingInterval);
          return;
        }
        setDisplayText(prevText => prevText + textAsString.charAt(i));
        i++;
      }, speed);

      return () => {
        clearInterval(typingInterval);
      };
    }
  }, [text, speed]);

  return displayText;
};


// --- ICON COMPONENTS ---
const CopyIcon: React.FC<{ className?: string }> = ({ className = "h-5 w-5" }) => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
    </svg>
);

const ShareIcon: React.FC<{ className?: string }> = ({ className = "h-5 w-5" }) => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12s-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6.002l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.368a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
    </svg>
);

const CheckIcon: React.FC<{ className?: string }> = ({ className = "h-5 w-5 text-green-400" }) => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
    </svg>
);

// --- UI COMPONENTS ---
const CipherKey: React.FC = () => {
    return (
        <div className="w-full max-w-4xl mx-auto mt-8 p-4 border border-green-700 rounded-lg bg-gray-900/50">
            <h3 className="text-center text-green-400 text-lg mb-4 font-bold tracking-widest">CIPHER KEY</h3>
            <div className="grid grid-cols-4 sm:grid-cols-6 md:grid-cols-9 gap-2 text-sm text-green-300">
                {Object.entries(CIPHER_MAP).map(([key, value]) => (
                    <div key={key} className="flex items-baseline">
                        <span className="font-bold mr-2">{key === ' ' ? 'SPACE' : key.toUpperCase()}:</span>
                        <span className="text-green-500">{value}</span>
                    </div>
                ))}
            </div>
        </div>
    );
};

// --- MAIN APP COMPONENT ---
type Mode = 'coder' | 'decoder';

const App: React.FC = () => {
    const [mode, setMode] = useState<Mode>('coder');
    const [input, setInput] = useState<string>('');
    const [rawOutput, setRawOutput] = useState<string>('');
    const [isProcessing, setIsProcessing] = useState<boolean>(false);
    const [copied, setCopied] = useState<boolean>(false);

    const typewriterOutput = useTypewriter(rawOutput);

    const reversedCipher = useMemo(() => {
        return Object.entries(CIPHER_MAP).reduce((acc, [key, value]) => {
            acc[value] = key;
            return acc;
        }, {} as Record<string, string>);
    }, []);

    const handleEncrypt = useCallback((text: string) => {
        return text
            .toLowerCase()
            .split('')
            .map(char => CIPHER_MAP[char as CipherKey] || '')
            .filter(Boolean)
            .join(' ');
    }, []);
    
    const handleDecrypt = useCallback((code: string) => {
        return code
            .trim()
            .split(' ')
            .map(c => reversedCipher[c] || (c ? '[?]' : ''))
            .join('');
    }, [reversedCipher]);

    const handleProcess = useCallback(() => {
        if (!input.trim()) return;

        setIsProcessing(true);
        setRawOutput('');

        setTimeout(() => {
            const result = mode === 'coder' ? handleEncrypt(input) : handleDecrypt(input);
            setRawOutput(result);
            setIsProcessing(false);
        }, 300);
    }, [input, mode, handleEncrypt, handleDecrypt]);

    const toggleMode = () => {
        setMode(prevMode => (prevMode === 'coder' ? 'decoder' : 'coder'));
        setInput('');
        setRawOutput('');
    };
    
    const handleCopy = useCallback(() => {
        if (!rawOutput) return;
        navigator.clipboard.writeText(rawOutput);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
    }, [rawOutput]);

    const handleShare = useCallback(async () => {
        if (!rawOutput) return;
        if (navigator.share) {
            try {
                await navigator.share({ title: 'SpyLang Message', text: rawOutput });
            } catch (error) {
                console.error('Error sharing:', error);
            }
        } else {
            alert('Share API not supported in your browser.');
        }
    }, [rawOutput]);
    
    const glowingShadow = 'shadow-[0_0_8px_rgba(74,222,128,0.4)]';

    return (
        <div className="min-h-screen bg-black text-green-400 p-4 sm:p-6 lg:p-8 flex flex-col items-center">
            <header className="text-center mb-8">
                <h1 className="text-4xl md:text-5xl font-bold tracking-widest animate-pulse">
                    SPYLANG
                </h1>
                <p className="text-green-500">A/B Secret Message Bureau</p>
            </header>

            <div className="w-full max-w-4xl mb-6 flex-grow flex flex-col">
                <div className="flex justify-center items-center space-x-4 mb-4">
                    <label htmlFor="mode-toggle" className={`transition-opacity cursor-pointer ${mode === 'decoder' ? 'opacity-50' : 'font-bold'}`}>
                        CODER
                    </label>
                    <label htmlFor="mode-toggle" className="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" id="mode-toggle" className="sr-only peer" checked={mode === 'decoder'} onChange={toggleMode} />
                        <div className={`w-14 h-7 bg-gray-800 rounded-full border border-green-700 peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[4px] after:bg-green-500 after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-900 ${glowingShadow}`}></div>
                    </label>
                    <label htmlFor="mode-toggle" className={`transition-opacity cursor-pointer ${mode === 'coder' ? 'opacity-50' : 'font-bold'}`}>
                        DECODER
                    </label>
                </div>

                <div className="grid grid-cols-1 gap-4 flex-grow">
                    <div className="relative">
                        <textarea
                            value={input}
                            onChange={(e) => setInput(e.target.value)}
                            placeholder={mode === 'coder' ? 'Enter your message here...' : 'Paste A/B code here...'}
                            className={`w-full h-full min-h-[12rem] p-4 bg-gray-900/70 border border-green-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 resize-none caret-green-400 ${glowingShadow} transition-shadow duration-300`}
                        />
                         <div className="absolute bottom-2 right-2 text-xs text-green-700">{input.length}</div>
                    </div>
                    
                    <div className="flex justify-center">
                         <button
                            onClick={handleProcess}
                            disabled={isProcessing || !input.trim()}
                            className={`px-8 py-3 bg-green-600 text-black font-bold rounded-lg hover:bg-green-500 transition-all duration-300 disabled:bg-gray-700 disabled:text-gray-400 disabled:cursor-not-allowed transform hover:scale-105 ${glowingShadow} w-full sm:w-auto`}
                        >
                            {isProcessing ? 'Processing...' : (mode === 'coder' ? 'ENCRYPT' : 'DECRYPT')}
                        </button>
                    </div>

                    <div className="relative">
                        <textarea
                            readOnly
                            value={typewriterOutput}
                            placeholder="Output will appear here..."
                            className={`w-full h-full min-h-[12rem] p-4 bg-gray-900/70 border border-green-700 rounded-lg focus:outline-none resize-none ${glowingShadow}`}
                        />
                        {rawOutput && (
                             <div className="absolute top-2 right-2 flex items-center space-x-2">
                                {typeof navigator.share !== 'undefined' && (
                                     <button onClick={handleShare} title="Share" className="p-2 bg-gray-800/80 rounded-full hover:bg-green-800 transition-colors">
                                        <ShareIcon />
                                    </button>
                                )}
                                <button onClick={handleCopy} title="Copy" className="p-2 bg-gray-800/80 rounded-full hover:bg-green-800 transition-colors">
                                    {copied ? <CheckIcon /> : <CopyIcon />}
                                </button>
                            </div>
                        )}
                         <div className="absolute bottom-2 right-2 text-xs text-green-700">{typewriterOutput.length}</div>
                    </div>
                </div>
            </div>

            <CipherKey />
            
            <footer className="mt-auto pt-8 text-center text-green-700 text-sm">
                <p>&copy; 2024 SpyLang Bureau. For authorized eyes only.</p>
            </footer>
        </div>
    );
};

export default App;
