<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 Web Adventure Game</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            color: white;
        }
        
        .game-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .stats-bar {
            display: flex;
            justify-content: space-around;
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .stat {
            text-align: center;
            font-weight: bold;
        }
        
        .game-area {
            display: flex;
            gap: 20px;
            min-height: 400px;
        }
        
        .scene-panel {
            flex: 1;
            background: linear-gradient(45deg, #3498db, #2980b9);
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            font-size: 18px;
            line-height: 1.6;
        }
        
        .control-panel {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
        }
        
        .story-text {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            padding: 15px;
            border-radius: 8px;
            height: 200px;
            overflow-y: auto;
            margin-bottom: 20px;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .action-btn {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        .action-btn.forest { background: linear-gradient(45deg, #27ae60, #229954); }
        .action-btn.mountain { background: linear-gradient(45deg, #95a5a6, #7f8c8d); }
        .action-btn.desert { background: linear-gradient(45deg, #f39c12, #e67e22); }
        .action-btn.combat { background: linear-gradient(45deg, #e74c3c, #c0392b); }
        
        .inventory {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 8px;
            margin-top: 20px;
            text-align: center;
        }
        
        .scene-forest { background: linear-gradient(45deg, #27ae60, #229954); }
        .scene-mountain { background: linear-gradient(45deg, #95a5a6, #7f8c8d); }
        .scene-desert { background: linear-gradient(45deg, #f39c12, #e67e22); }
        .scene-combat { background: linear-gradient(45deg, #e74c3c, #c0392b); }
        .scene-victory { background: linear-gradient(45deg, #2ecc71, #27ae60); }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .pulse { animation: pulse 2s infinite; }
    </style>
</head>
<body>
    <div class="game-container">
        <div class="header">
            <h1>🎮 Web Adventure Game 🎮</h1>
            <p>A graphic adventure game in your browser!</p>
        </div>
        
        <div class="stats-bar">
            <div class="stat">
                <div>👤 Player</div>
                <div id="player-name">Adventurer</div>
            </div>
            <div class="stat">
                <div>❤️ Health</div>
                <div id="health">100/100</div>
            </div>
            <div class="stat">
                <div>⭐ Level</div>
                <div id="level">1</div>
            </div>
            <div class="stat">
                <div>💰 Gold</div>
                <div id="gold">0</div>
            </div>
        </div>
        
        <div class="game-area">
            <div class="scene-panel" id="scene-panel">
                <div class="pulse">
                    <h2>🗺️ The Crossroads</h2>
                    <p>Three mysterious paths stretch before you...</p>
                    <p>Each leads to different adventures and challenges!</p>
                    <p>Choose your destiny, brave adventurer!</p>
                </div>
            </div>
            
            <div class="control-panel">
                <div class="story-text" id="story-text">
                    <p><strong>🌟 Welcome to the Web Adventure Game! 🌟</strong></p>
                    <p>You stand at the crossroads of destiny. Three paths await your choice:</p>
                    <p>🌲 <strong>Forest Path:</strong> Ancient trees whisper secrets of magic and mystery.</p>
                    <p>🏔️ <strong>Mountain Path:</strong> Steep trails lead to dragons and legendary treasures.</p>
                    <p>🏜️ <strong>Desert Path:</strong> Endless sands hide ancient pyramids and forgotten secrets.</p>
                    <p>Choose wisely, for your adventure begins now!</p>
                </div>
                
                <div class="action-buttons" id="action-buttons">
                    <button class="action-btn forest" onclick="chooseForest()">🌲 Forest Path</button>
                    <button class="action-btn mountain" onclick="chooseMountain()">🏔️ Mountain Path</button>
                    <button class="action-btn desert" onclick="chooseDesert()">🏜️ Desert Path</button>
                    <button class="action-btn" onclick="showStats()">📊 View Stats</button>
                </div>
            </div>
        </div>
        
        <div class="inventory">
            <strong>🎒 Inventory:</strong> <span id="inventory">Empty</span>
        </div>
    </div>

    <script>
        // Game state
        let player = {
            name: "Adventurer",
            health: 100,
            maxHealth: 100,
            attack: 10,
            defense: 5,
            gold: 0,
            level: 1,
            experience: 0,
            inventory: []
        };
        
        let currentEnemy = null;
        
        // Initialize game
        function initGame() {
            const name = prompt("What is your name, brave adventurer?") || "Adventurer";
            player.name = name;
            updateDisplay();
        }
        
        function updateDisplay() {
            document.getElementById('player-name').textContent = player.name;
            document.getElementById('health').textContent = `${player.health}/${player.maxHealth}`;
            document.getElementById('level').textContent = player.level;
            document.getElementById('gold').textContent = player.gold;
            document.getElementById('inventory').textContent = player.inventory.length > 0 ? player.inventory.join(', ') : 'Empty';
        }
        
        function addStoryText(text) {
            const storyDiv = document.getElementById('story-text');
            storyDiv.innerHTML += `<p>${text}</p>`;
            storyDiv.scrollTop = storyDiv.scrollHeight;
        }
        
        function clearStoryText() {
            document.getElementById('story-text').innerHTML = '';
        }
        
        function updateScene(title, description, className = '') {
            const scenePanel = document.getElementById('scene-panel');
            scenePanel.className = `scene-panel ${className}`;
            scenePanel.innerHTML = `
                <div class="pulse">
                    <h2>${title}</h2>
                    <p>${description}</p>
                </div>
            `;
        }
        
        function setActionButtons(buttons) {
            const buttonsDiv = document.getElementById('action-buttons');
            buttonsDiv.innerHTML = '';
            
            buttons.forEach(button => {
                const btn = document.createElement('button');
                btn.className = `action-btn ${button.class || ''}`;
                btn.textContent = button.text;
                btn.onclick = button.action;
                buttonsDiv.appendChild(btn);
            });
        }
        
        // Path choices
        function chooseForest() {
            updateScene('🌲 Enchanted Forest', 'Magical creatures dart between ancient trees...', 'scene-forest');
            clearStoryText();
            addStoryText('🌲 You venture into the Enchanted Forest...');
            addStoryText('Sunlight filters through ancient trees, and magical creatures dart between shadows.');
            
            const encounter = Math.random();
            if (encounter < 0.33) {
                fairyEncounter();
            } else if (encounter < 0.66) {
                treasureEncounter();
            } else {
                wolfEncounter();
            }
        }
        
        function chooseMountain() {
            updateScene('🏔️ Mountain Path', 'Steep rocky trails disappear into the mist...', 'scene-mountain');
            clearStoryText();
            addStoryText('🏔️ You begin the treacherous climb up the mountain...');
            addStoryText('The air grows thin and cold as you ascend the rocky path.');
            
            const encounter = Math.random();
            if (encounter < 0.5) {
                dragonEncounter();
            } else {
                mountainTreasure();
            }
        }
        
        function chooseDesert() {
            updateScene('🏜️ Desert Sands', 'Endless dunes stretch under the burning sun...', 'scene-desert');
            clearStoryText();
            addStoryText('🏜️ You venture into the vast desert...');
            addStoryText('The sun beats down mercilessly as sand stretches endlessly.');
            
            if (Math.random() < 0.6) {
                oasisEncounter();
            } else {
                banditEncounter();
            }
        }
        
        // Encounters
        function fairyEncounter() {
            addStoryText('✨ A glowing fairy approaches with a warm smile!');
            addStoryText('"I can grant you a gift, traveler!"');
            
            setActionButtons([
                { text: '💚 Ask for Healing', action: () => fairyGift('healing') },
                { text: '⚔️ Ask for Strength', action: () => fairyGift('strength') },
                { text: '🏠 Return to Crossroads', action: returnToCrossroads }
            ]);
        }
        
        function fairyGift(type) {
            if (type === 'healing') {
                const healed = Math.min(50, player.maxHealth - player.health);
                player.health += healed;
                addStoryText(`✨ The fairy heals you for ${healed} HP!`);
            } else {
                player.attack += 5;
                addStoryText('✨ The fairy blesses you with strength!');
                addStoryText('⚔️ Your attack power increased!');
            }
            
            updateDisplay();
            setActionButtons([
                { text: '🏠 Return to Crossroads', action: returnToCrossroads },
                { text: '🌲 Continue in Forest', action: chooseForest }
            ]);
        }
        
        function treasureEncounter() {
            addStoryText('💎 You discover a treasure chest!');
            const gold = Math.floor(Math.random() * 30) + 20;
            player.gold += gold;
            addStoryText(`💰 You found ${gold} gold!`);
            
            updateDisplay();
            setActionButtons([
                { text: '🏠 Return to Crossroads', action: returnToCrossroads },
                { text: '🌲 Continue in Forest', action: chooseForest }
            ]);
        }
        
        function wolfEncounter() {
            addStoryText('🐺 A fierce wolf blocks your path!');
            currentEnemy = { name: 'Forest Wolf', health: 40, attack: 12, xp: 25, gold: 15 };
            
            setActionButtons([
                { text: '⚔️ Fight Wolf', action: startCombat, class: 'combat' },
                { text: '🏃 Run Away', action: returnToCrossroads }
            ]);
        }
        
        function dragonEncounter() {
            addStoryText('🐉 You discover a dragon\'s cave!');
            addStoryText('The mighty beast sleeps on a pile of gold.');
            
            setActionButtons([
                { text: '⚔️ Challenge Dragon', action: () => fightDragon() },
                { text: '🤫 Sneak Past', action: sneakPastDragon },
                { text: '🏃 Retreat', action: returnToCrossroads }
            ]);
        }
        
        function fightDragon() {
            currentEnemy = { name: 'Ancient Dragon', health: 80, attack: 20, xp: 100, gold: 150 };
            startCombat();
        }
        
        function sneakPastDragon() {
            if (Math.random() < 0.4) {
                const gold = Math.floor(Math.random() * 40) + 30;
                player.gold += gold;
                addStoryText(`🤫 You successfully sneak past and grab ${gold} gold!`);
                updateDisplay();
                setActionButtons([{ text: '🏠 Return to Crossroads', action: returnToCrossroads }]);
            } else {
                addStoryText('👁️ The dragon spots you!');
                fightDragon();
            }
        }
        
        function mountainTreasure() {
            addStoryText('💎 You find an ancient treasure hoard!');
            const gold = Math.floor(Math.random() * 40) + 40;
            player.gold += gold;
            player.experience += 30;
            addStoryText(`💰 You found ${gold} gold!`);
            addStoryText('⭐ You gained 30 XP!');
            
            checkLevelUp();
            updateDisplay();
            setActionButtons([
                { text: '🏠 Return to Crossroads', action: returnToCrossroads },
                { text: '🏔️ Continue Climbing', action: chooseMountain }
            ]);
        }
        
        function oasisEncounter() {
            addStoryText('🌴 You find an oasis!');
            const healed = Math.min(30, player.maxHealth - player.health);
            player.health += healed;
            player.inventory.push('Water Flask');
            addStoryText(`💧 You drink deeply and heal ${healed} HP!`);
            addStoryText('💧 You fill your water flask.');
            
            updateDisplay();
            setActionButtons([
                { text: '🏠 Return to Crossroads', action: returnToCrossroads },
                { text: '🏜️ Continue in Desert', action: chooseDesert }
            ]);
        }
        
        function banditEncounter() {
            addStoryText('🏴‍☠️ Desert bandits attack!');
            currentEnemy = { name: 'Desert Bandit', health: 35, attack: 10, xp: 20, gold: 25 };
            
            setActionButtons([
                { text: '⚔️ Fight Bandit', action: startCombat, class: 'combat' },
                { text: '🏃 Run Away', action: returnToCrossroads }
            ]);
        }
        
        // Combat system
        function startCombat() {
            updateScene(`⚔️ COMBAT!`, `${player.name} vs ${currentEnemy.name}`, 'scene-combat');
            addStoryText(`⚔️ COMBAT: ${player.name} vs ${currentEnemy.name}!`);
            addStoryText(`Enemy: ${currentEnemy.health} HP`);
            
            combatTurn();
        }
        
        function combatTurn() {
            if (currentEnemy.health <= 0) {
                combatVictory();
                return;
            }
            
            if (player.health <= 0) {
                gameOver();
                return;
            }
            
            setActionButtons([
                { text: '⚔️ Attack', action: playerAttack, class: 'combat' },
                { text: '🛡️ Defend', action: playerDefend },
                { text: '🏃 Try to Run', action: tryRun }
            ]);
        }
        
        function playerAttack() {
            const damage = Math.floor(Math.random() * 4) + player.attack - 2;
            currentEnemy.health -= damage;
            addStoryText(`💥 You deal ${damage} damage! Enemy health: ${Math.max(0, currentEnemy.health)}`);
            
            if (currentEnemy.health <= 0) {
                combatVictory();
            } else {
                enemyAttack();
            }
        }
        
        function playerDefend() {
            addStoryText('🛡️ You brace for the enemy\'s attack!');
            enemyAttack(true);
        }
        
        function enemyAttack(defending = false) {
            let damage = Math.floor(Math.random() * 3) + currentEnemy.attack - 1;
            if (defending) damage = Math.max(1, damage - 3);
            
            const actualDamage = Math.max(1, damage - player.defense);
            player.health -= actualDamage;
            
            addStoryText(`🐉 ${currentEnemy.name} attacks!`);
            addStoryText(`💥 You take ${actualDamage} damage! Health: ${Math.max(0, player.health)}/${player.maxHealth}`);
            
            updateDisplay();
            
            if (player.health <= 0) {
                gameOver();
            } else {
                setTimeout(combatTurn, 1000);
            }
        }
        
        function combatVictory() {
            addStoryText(`🎉 Victory! You defeated ${currentEnemy.name}!`);
            
            player.experience += currentEnemy.xp;
            player.gold += currentEnemy.gold;
            
            addStoryText(`⭐ You gained ${currentEnemy.xp} XP!`);
            addStoryText(`💰 You found ${currentEnemy.gold} gold!`);
            
            checkLevelUp();
            currentEnemy = null;
            updateDisplay();
            
            if (player.level >= 5) {
                victoryEnding();
            } else {
                setActionButtons([{ text: '🏠 Return to Crossroads', action: returnToCrossroads }]);
            }
        }
        
        function tryRun() {
            if (Math.random() < 0.6) {
                addStoryText('💨 You successfully ran away!');
                currentEnemy = null;
                returnToCrossroads();
            } else {
                addStoryText('❌ Couldn\'t escape!');
                enemyAttack();
            }
        }
        
        function checkLevelUp() {
            while (player.experience >= player.level * 100) {
                player.level++;
                player.maxHealth += 20;
                player.health = player.maxHealth;
                player.attack += 3;
                player.defense += 2;
                addStoryText(`🎉 LEVEL UP! You are now level ${player.level}!`);
            }
        }
        
        function returnToCrossroads() {
            updateScene('🗺️ The Crossroads', 'Three mysterious paths await your choice...', '');
            clearStoryText();
            addStoryText('🏰 You return to the crossroads.');
            addStoryText('Three paths stretch before you once again...');
            
            setActionButtons([
                { text: '🌲 Forest Path', action: chooseForest, class: 'forest' },
                { text: '🏔️ Mountain Path', action: chooseMountain, class: 'mountain' },
                { text: '🏜️ Desert Path', action: chooseDesert, class: 'desert' },
                { text: '📊 View Stats', action: showStats }
            ]);
        }
        
        function showStats() {
            alert(`📊 ${player.name}'s Stats:
            
❤️ Health: ${player.health}/${player.maxHealth}
⚔️ Attack: ${player.attack}
🛡️ Defense: ${player.defense}
💰 Gold: ${player.gold}
⭐ Level: ${player.level}
🎯 Experience: ${player.experience}

🎒 Inventory: ${player.inventory.length > 0 ? player.inventory.join(', ') : 'Empty'}`);
        }
        
        function gameOver() {
            updateScene('💀 GAME OVER', 'Your adventure ends here...', 'scene-combat');
            addStoryText('💀 GAME OVER');
            
            if (confirm(`💀 ${player.name} has fallen!\n\nWould you like to start a new adventure?`)) {
                location.reload();
            }
        }
        
        function victoryEnding() {
            updateScene('🏆 VICTORY!', 'You are now a legendary adventurer!', 'scene-victory');
            addStoryText('🏆 VICTORY! You have become a legendary adventurer!');
            
            if (confirm(`🏆 Congratulations ${player.name}!\n\nYou've reached level ${player.level}!\n\nStart a new adventure?`)) {
                location.reload();
            }
        }
        
        // Start the game
        window.onload = function() {
            initGame();
        };
    </script>
</body>
</html>
