#!/usr/bin/env python3
"""
Simple Modern Password Manager
A clean, minimal password manager with modern GUI design.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
import secrets
import string
import hashlib
import base64

class SimplePasswordManager:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔐 SecureVault - Simple")
        self.root.geometry("800x600")
        self.root.configure(bg='#1a1a2e')
        
        # Modern color scheme
        self.colors = {
            'bg_primary': '#1a1a2e',
            'bg_secondary': '#16213e',
            'bg_card': '#0f3460',
            'accent': '#533483',
            'text_primary': '#ffffff',
            'text_secondary': '#a0a0a0',
            'success': '#00d4aa',
            'warning': '#ffbe0b',
            'danger': '#fb5607',
            'button_bg': '#667eea'
        }
        
        # Data storage
        self.data_file = "simple_passwords.json"
        self.passwords = {}
        self.master_password = None
        
        # Setup styles
        self.setup_styles()
        self.create_login_screen()
    
    def setup_styles(self):
        """Setup modern styling."""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure button style
        style.configure('Modern.TButton',
                       background=self.colors['button_bg'],
                       foreground='white',
                       font=('Segoe UI', 10, 'bold'),
                       borderwidth=0,
                       focuscolor='none')
        
        style.map('Modern.TButton',
                 background=[('active', self.colors['accent'])])
        
        # Configure frame styles
        style.configure('Card.TFrame',
                       background=self.colors['bg_card'],
                       relief='flat',
                       borderwidth=2)
    
    def create_login_screen(self):
        """Create the login screen."""
        # Clear window
        for widget in self.root.winfo_children():
            widget.destroy()
        
        # Main container
        main_frame = tk.Frame(self.root, bg=self.colors['bg_primary'])
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Center container
        center_frame = tk.Frame(main_frame, bg=self.colors['bg_primary'])
        center_frame.place(relx=0.5, rely=0.5, anchor='center')
        
        # Title
        title_label = tk.Label(center_frame, text="🔐 SecureVault", 
                              font=('Segoe UI', 28, 'bold'),
                              fg=self.colors['text_primary'], bg=self.colors['bg_primary'])
        title_label.pack(pady=(0, 10))
        
        subtitle_label = tk.Label(center_frame, text="Simple & Secure Password Manager", 
                                 font=('Segoe UI', 12),
                                 fg=self.colors['text_secondary'], bg=self.colors['bg_primary'])
        subtitle_label.pack(pady=(0, 30))
        
        # Login card
        card_frame = ttk.Frame(center_frame, style='Card.TFrame', padding=40)
        card_frame.pack(pady=20)
        
        # Card title
        card_title = tk.Label(card_frame, text="Master Password", 
                             font=('Segoe UI', 16, 'bold'),
                             fg=self.colors['text_primary'], bg=self.colors['bg_card'])
        card_title.pack(pady=(0, 20))
        
        # Password entry
        self.login_entry = tk.Entry(card_frame, font=('Segoe UI', 12), show="*",
                                   bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                                   insertbackground='white', width=25, relief='flat', bd=10)
        self.login_entry.pack(pady=10, ipady=8)
        
        # Buttons
        buttons_frame = tk.Frame(card_frame, bg=self.colors['bg_card'])
        buttons_frame.pack(pady=20)
        
        login_btn = ttk.Button(buttons_frame, text="🔓 Unlock", style='Modern.TButton',
                              command=self.login, width=15)
        login_btn.pack(side=tk.LEFT, padx=5)
        
        create_btn = ttk.Button(buttons_frame, text="✨ Create New", style='Modern.TButton',
                               command=self.create_new_vault, width=15)
        create_btn.pack(side=tk.LEFT, padx=5)
        
        # Bind Enter key
        self.login_entry.bind('<Return>', lambda e: self.login())
        self.login_entry.focus()
    
    def hash_password(self, password):
        """Simple password hashing."""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def login(self):
        """Handle login."""
        password = self.login_entry.get()
        if not password:
            messagebox.showwarning("Warning", "Please enter your master password!")
            return
        
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r') as f:
                    data = json.load(f)
                
                stored_hash = data.get('master_hash')
                if stored_hash == self.hash_password(password):
                    self.passwords = data.get('passwords', {})
                    self.master_password = password
                    self.create_main_interface()
                else:
                    messagebox.showerror("Error", "Invalid master password!")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load vault: {e}")
        else:
            messagebox.showwarning("Warning", "No vault found. Create a new one!")
    
    def create_new_vault(self):
        """Create new vault."""
        password = self.login_entry.get()
        if len(password) < 6:
            messagebox.showwarning("Warning", "Master password must be at least 6 characters!")
            return
        
        self.passwords = {}
        self.master_password = password
        self.save_data()
        messagebox.showinfo("Success", "Vault created successfully!")
        self.create_main_interface()
    
    def save_data(self):
        """Save password data."""
        data = {
            'master_hash': self.hash_password(self.master_password),
            'passwords': self.passwords
        }
        
        with open(self.data_file, 'w') as f:
            json.dump(data, f, indent=2)
    
    def create_main_interface(self):
        """Create main interface."""
        # Clear window
        for widget in self.root.winfo_children():
            widget.destroy()
        
        # Main container
        main_frame = tk.Frame(self.root, bg=self.colors['bg_primary'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Header
        header_frame = tk.Frame(main_frame, bg=self.colors['bg_primary'])
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Title
        title_label = tk.Label(header_frame, text="🔐 SecureVault", 
                              font=('Segoe UI', 20, 'bold'),
                              fg=self.colors['text_primary'], bg=self.colors['bg_primary'])
        title_label.pack(side=tk.LEFT)
        
        # Header buttons
        header_buttons = tk.Frame(header_frame, bg=self.colors['bg_primary'])
        header_buttons.pack(side=tk.RIGHT)
        
        ttk.Button(header_buttons, text="➕ Add", style='Modern.TButton',
                  command=self.show_add_dialog, width=12).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(header_buttons, text="🎲 Generate", style='Modern.TButton',
                  command=self.show_generate_dialog, width=12).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(header_buttons, text="🚪 Logout", style='Modern.TButton',
                  command=self.logout, width=12).pack(side=tk.LEFT, padx=2)
        
        # Search
        search_frame = tk.Frame(main_frame, bg=self.colors['bg_primary'])
        search_frame.pack(fill=tk.X, pady=(0, 20))
        
        tk.Label(search_frame, text="🔍 Search:", font=('Segoe UI', 12),
                fg=self.colors['text_primary'], bg=self.colors['bg_primary']).pack(side=tk.LEFT, padx=(0, 10))
        
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.filter_passwords)
        search_entry = tk.Entry(search_frame, textvariable=self.search_var, font=('Segoe UI', 11),
                               bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                               insertbackground='white', width=40, relief='flat', bd=5)
        search_entry.pack(side=tk.LEFT, ipady=5)
        
        # Passwords list
        self.create_passwords_list(main_frame)
        self.refresh_password_list()
    
    def create_passwords_list(self, parent):
        """Create scrollable passwords list."""
        # Frame for treeview
        tree_frame = tk.Frame(parent, bg=self.colors['bg_primary'])
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        # Treeview
        columns = ('Site', 'Username', 'Actions')
        self.tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        self.tree.heading('Site', text='🌐 Website/Service')
        self.tree.heading('Username', text='👤 Username')
        self.tree.heading('Actions', text='⚙️ Actions')
        
        self.tree.column('Site', width=250)
        self.tree.column('Username', width=250)
        self.tree.column('Actions', width=200)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Bind double-click
        self.tree.bind('<Double-1>', self.on_item_select)
        
        # Context menu
        self.create_context_menu()
    
    def create_context_menu(self):
        """Create right-click context menu."""
        self.context_menu = tk.Menu(self.root, tearoff=0, bg=self.colors['bg_card'],
                                   fg=self.colors['text_primary'])
        self.context_menu.add_command(label="📋 Copy Password", command=self.copy_password)
        self.context_menu.add_command(label="✏️ Edit", command=self.edit_password)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="🗑️ Delete", command=self.delete_password)
        
        self.tree.bind('<Button-3>', self.show_context_menu)
    
    def show_context_menu(self, event):
        """Show context menu."""
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)
    
    def refresh_password_list(self, filter_text=""):
        """Refresh password list."""
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # Add passwords
        for site, data in self.passwords.items():
            if filter_text.lower() in site.lower() or filter_text.lower() in data.get('username', '').lower():
                username = data.get('username', 'N/A')
                password_masked = '•' * min(len(data.get('password', '')), 12)
                
                self.tree.insert('', tk.END, values=(site, username, f"🔑 {password_masked}"))
    
    def filter_passwords(self, *args):
        """Filter passwords based on search."""
        search_text = self.search_var.get()
        self.refresh_password_list(search_text)
    
    def on_item_select(self, event):
        """Handle item selection."""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            site = item['values'][0]
            self.copy_password_for_site(site)
    
    def copy_password(self):
        """Copy selected password."""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            site = item['values'][0]
            self.copy_password_for_site(site)
    
    def copy_password_for_site(self, site):
        """Copy password for specific site."""
        if site in self.passwords:
            password = self.passwords[site]['password']
            self.root.clipboard_clear()
            self.root.clipboard_append(password)
            messagebox.showinfo("Success", f"Password for {site} copied to clipboard!")
    
    def edit_password(self):
        """Edit selected password."""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            site = item['values'][0]
            if site in self.passwords:
                data = self.passwords[site]
                self.show_password_dialog(site, data['username'], data['password'])
    
    def delete_password(self):
        """Delete selected password."""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            site = item['values'][0]
            
            if messagebox.askyesno("Confirm Delete", f"Delete password for {site}?"):
                if site in self.passwords:
                    del self.passwords[site]
                    self.save_data()
                    self.refresh_password_list()
                    messagebox.showinfo("Success", f"Password for {site} deleted!")
    
    def show_add_dialog(self):
        """Show add password dialog."""
        self.show_password_dialog()
    
    def show_password_dialog(self, site="", username="", password=""):
        """Show password dialog."""
        dialog = tk.Toplevel(self.root)
        dialog.title("Add Password" if not site else "Edit Password")
        dialog.geometry("400x300")
        dialog.configure(bg=self.colors['bg_primary'])
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()
        
        # Center dialog
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 200, self.root.winfo_rooty() + 150))
        
        # Content frame
        content_frame = ttk.Frame(dialog, style='Card.TFrame', padding=30)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Title
        title_text = "Add New Password" if not site else f"Edit: {site}"
        tk.Label(content_frame, text=title_text, font=('Segoe UI', 14, 'bold'),
                fg=self.colors['text_primary'], bg=self.colors['bg_card']).pack(pady=(0, 20))
        
        # Site entry
        tk.Label(content_frame, text="Website/Service:", font=('Segoe UI', 10),
                fg=self.colors['text_primary'], bg=self.colors['bg_card']).pack(anchor='w')
        site_entry = tk.Entry(content_frame, font=('Segoe UI', 11), width=30,
                             bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                             insertbackground='white', relief='flat', bd=5)
        site_entry.pack(pady=(5, 10), ipady=5)
        if site:
            site_entry.insert(0, site)
        
        # Username entry
        tk.Label(content_frame, text="Username/Email:", font=('Segoe UI', 10),
                fg=self.colors['text_primary'], bg=self.colors['bg_card']).pack(anchor='w')
        username_entry = tk.Entry(content_frame, font=('Segoe UI', 11), width=30,
                                 bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                                 insertbackground='white', relief='flat', bd=5)
        username_entry.pack(pady=(5, 10), ipady=5)
        if username:
            username_entry.insert(0, username)
        
        # Password entry
        tk.Label(content_frame, text="Password:", font=('Segoe UI', 10),
                fg=self.colors['text_primary'], bg=self.colors['bg_card']).pack(anchor='w')
        password_entry = tk.Entry(content_frame, font=('Segoe UI', 11), width=30, show="*",
                                 bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                                 insertbackground='white', relief='flat', bd=5)
        password_entry.pack(pady=(5, 20), ipady=5)
        if password:
            password_entry.insert(0, password)
        
        # Buttons
        buttons_frame = tk.Frame(content_frame, bg=self.colors['bg_card'])
        buttons_frame.pack()
        
        def save_password():
            site_val = site_entry.get().strip()
            username_val = username_entry.get().strip()
            password_val = password_entry.get().strip()
            
            if not site_val or not username_val or not password_val:
                messagebox.showwarning("Warning", "Please fill all fields!")
                return
            
            self.passwords[site_val] = {
                'username': username_val,
                'password': password_val
            }
            
            self.save_data()
            self.refresh_password_list()
            messagebox.showinfo("Success", "Password saved successfully!")
            dialog.destroy()
        
        ttk.Button(buttons_frame, text="💾 Save", style='Modern.TButton',
                  command=save_password, width=12).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(buttons_frame, text="❌ Cancel", style='Modern.TButton',
                  command=dialog.destroy, width=12).pack(side=tk.LEFT, padx=5)
    
    def show_generate_dialog(self):
        """Show password generator."""
        dialog = tk.Toplevel(self.root)
        dialog.title("Password Generator")
        dialog.geometry("350x400")
        dialog.configure(bg=self.colors['bg_primary'])
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()
        
        # Center dialog
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 225, self.root.winfo_rooty() + 100))
        
        # Content
        content_frame = ttk.Frame(dialog, style='Card.TFrame', padding=20)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        tk.Label(content_frame, text="🎲 Password Generator", font=('Segoe UI', 14, 'bold'),
                fg=self.colors['text_primary'], bg=self.colors['bg_card']).pack(pady=(0, 20))
        
        # Length
        length_frame = tk.Frame(content_frame, bg=self.colors['bg_card'])
        length_frame.pack(fill=tk.X, pady=10)
        
        tk.Label(length_frame, text="Length:", font=('Segoe UI', 10),
                fg=self.colors['text_primary'], bg=self.colors['bg_card']).pack(side=tk.LEFT)
        
        length_var = tk.IntVar(value=12)
        length_spinbox = tk.Spinbox(length_frame, from_=8, to=32, textvariable=length_var,
                                   width=5, font=('Segoe UI', 10))
        length_spinbox.pack(side=tk.RIGHT)
        
        # Options
        options_frame = tk.Frame(content_frame, bg=self.colors['bg_card'])
        options_frame.pack(fill=tk.X, pady=10)
        
        uppercase_var = tk.BooleanVar(value=True)
        lowercase_var = tk.BooleanVar(value=True)
        numbers_var = tk.BooleanVar(value=True)
        symbols_var = tk.BooleanVar(value=True)
        
        tk.Checkbutton(options_frame, text="Uppercase (A-Z)", variable=uppercase_var,
                      bg=self.colors['bg_card'], fg=self.colors['text_primary'],
                      selectcolor=self.colors['accent']).pack(anchor='w')
        tk.Checkbutton(options_frame, text="Lowercase (a-z)", variable=lowercase_var,
                      bg=self.colors['bg_card'], fg=self.colors['text_primary'],
                      selectcolor=self.colors['accent']).pack(anchor='w')
        tk.Checkbutton(options_frame, text="Numbers (0-9)", variable=numbers_var,
                      bg=self.colors['bg_card'], fg=self.colors['text_primary'],
                      selectcolor=self.colors['accent']).pack(anchor='w')
        tk.Checkbutton(options_frame, text="Symbols (!@#$)", variable=symbols_var,
                      bg=self.colors['bg_card'], fg=self.colors['text_primary'],
                      selectcolor=self.colors['accent']).pack(anchor='w')
        
        # Generated password
        password_var = tk.StringVar()
        password_entry = tk.Entry(content_frame, textvariable=password_var, font=('Segoe UI', 11),
                                 bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                                 insertbackground='white', width=35, relief='flat', bd=5)
        password_entry.pack(pady=20, ipady=5)
        
        def generate_password():
            chars = ""
            if uppercase_var.get():
                chars += string.ascii_uppercase
            if lowercase_var.get():
                chars += string.ascii_lowercase
            if numbers_var.get():
                chars += string.digits
            if symbols_var.get():
                chars += "!@#$%^&*"
            
            if not chars:
                messagebox.showwarning("Warning", "Select at least one character type!")
                return
            
            password = ''.join(secrets.choice(chars) for _ in range(length_var.get()))
            password_var.set(password)
        
        def copy_generated():
            password = password_var.get()
            if password:
                self.root.clipboard_clear()
                self.root.clipboard_append(password)
                messagebox.showinfo("Success", "Password copied to clipboard!")
        
        # Buttons
        buttons_frame = tk.Frame(content_frame, bg=self.colors['bg_card'])
        buttons_frame.pack(pady=10)
        
        ttk.Button(buttons_frame, text="🎲 Generate", style='Modern.TButton',
                  command=generate_password, width=12).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(buttons_frame, text="📋 Copy", style='Modern.TButton',
                  command=copy_generated, width=12).pack(side=tk.LEFT, padx=5)
    
    def logout(self):
        """Logout."""
        self.passwords = {}
        self.master_password = None
        self.create_login_screen()
    
    def run(self):
        """Run the application."""
        self.root.mainloop()

def main():
    """Main function."""
    try:
        app = SimplePasswordManager()
        app.run()
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
