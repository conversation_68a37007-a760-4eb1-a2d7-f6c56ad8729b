#!/usr/bin/env python3
"""
Modern Password Manager
A sleek, minimal password manager with curved modern GUI design.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
import secrets
import string
import hashlib
import base64
import math

# Try to import cryptography for better security, but make it optional
try:
    from cryptography.fernet import Fernet
    CRYPTO_AVAILABLE = True
except ImportError:
    CRYPTO_AVAILABLE = False

class ModernPasswordManager:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔐 SecureVault")
        self.root.geometry("900x700")
        self.root.configure(bg='#0f0f23')
        self.root.resizable(False, False)

        # Modern color scheme
        self.colors = {
            'bg_primary': '#0f0f23',
            'bg_secondary': '#1a1a2e',
            'bg_card': '#16213e',
            'accent': '#0f3460',
            'accent_hover': '#533483',
            'text_primary': '#ffffff',
            'text_secondary': '#a0a0a0',
            'success': '#00d4aa',
            'warning': '#ffbe0b',
            'danger': '#fb5607',
            'input_bg': '#2a2a3e',
            'button_gradient_start': '#667eea',
            'button_gradient_end': '#764ba2'
        }

        # Data storage
        self.data_file = "passwords.enc"
        self.passwords = {}
        self.master_key = None
        self.cipher_suite = None

        # Setup GUI
        self.setup_styles()
        self.create_login_screen()

    def setup_styles(self):
        """Setup modern styling for ttk widgets."""
        style = ttk.Style()
        style.theme_use('clam')

        # Configure styles
        style.configure('Modern.TFrame', background=self.colors['bg_primary'])
        style.configure('Card.TFrame', background=self.colors['bg_card'], relief='flat')
        style.configure('Modern.TLabel', background=self.colors['bg_primary'],
                       foreground=self.colors['text_primary'], font=('Segoe UI', 10))
        style.configure('Title.TLabel', background=self.colors['bg_primary'],
                       foreground=self.colors['text_primary'], font=('Segoe UI', 24, 'bold'))
        style.configure('Subtitle.TLabel', background=self.colors['bg_primary'],
                       foreground=self.colors['text_secondary'], font=('Segoe UI', 12))

    def create_rounded_frame(self, parent, width, height, corner_radius=20, bg_color=None):
        """Create a rounded frame using Canvas."""
        if bg_color is None:
            bg_color = self.colors['bg_card']

        canvas = tk.Canvas(parent, width=width, height=height,
                          bg=self.colors['bg_primary'], highlightthickness=0)

        # Draw rounded rectangle
        self.draw_rounded_rectangle(canvas, 0, 0, width, height, corner_radius, bg_color)

        # Create frame inside canvas
        frame = tk.Frame(canvas, bg=bg_color)
        canvas.create_window(width//2, height//2, window=frame)

        return canvas, frame

    def draw_rounded_rectangle(self, canvas, x1, y1, x2, y2, radius, fill_color):
        """Draw a rounded rectangle on canvas."""
        points = []

        # Top side
        points.extend([x1 + radius, y1])
        points.extend([x2 - radius, y1])

        # Top right corner
        for i in range(0, 90, 5):
            angle = i * 3.14159 / 180
            px = x2 - radius + radius * (1 - math.cos(angle))
            py = y1 + radius - radius * math.sin(angle)
            points.extend([px, py])

        # Right side
        points.extend([x2, y1 + radius])
        points.extend([x2, y2 - radius])

        # Bottom right corner
        for i in range(90, 180, 5):
            angle = i * 3.14159 / 180
            px = x2 - radius + radius * (1 - math.cos(angle))
            py = y2 - radius - radius * math.sin(angle)
            points.extend([px, py])

        # Bottom side
        points.extend([x2 - radius, y2])
        points.extend([x1 + radius, y2])

        # Bottom left corner
        for i in range(180, 270, 5):
            angle = i * 3.14159 / 180
            px = x1 + radius + radius * (1 - math.cos(angle))
            py = y2 - radius - radius * math.sin(angle)
            points.extend([px, py])

        # Left side
        points.extend([x1, y2 - radius])
        points.extend([x1, y1 + radius])

        # Top left corner
        for i in range(270, 360, 5):
            angle = i * 3.14159 / 180
            px = x1 + radius + radius * (1 - math.cos(angle))
            py = y1 + radius - radius * math.sin(angle)
            points.extend([px, py])

        canvas.create_polygon(points, fill=fill_color, outline=fill_color, smooth=True)

    def create_modern_button(self, parent, text, command, width=200, height=45,
                           bg_color=None, text_color='white', font_size=11):
        """Create a modern gradient button."""
        if bg_color is None:
            bg_color = self.colors['button_gradient_start']

        canvas = tk.Canvas(parent, width=width, height=height,
                          bg=self.colors['bg_primary'], highlightthickness=0)

        # Draw rounded button background
        self.draw_rounded_rectangle(canvas, 2, 2, width-2, height-2, 15, bg_color)

        # Add text
        canvas.create_text(width//2, height//2, text=text, fill=text_color,
                          font=('Segoe UI', font_size, 'bold'))

        # Bind click event
        canvas.bind("<Button-1>", lambda e: command())
        canvas.bind("<Enter>", lambda e: self.on_button_hover(canvas, width, height, True))
        canvas.bind("<Leave>", lambda e: self.on_button_hover(canvas, width, height, False))

        return canvas

    def on_button_hover(self, canvas, width, height, is_hover):
        """Handle button hover effect."""
        canvas.delete("all")
        color = self.colors['button_gradient_end'] if is_hover else self.colors['button_gradient_start']
        self.draw_rounded_rectangle(canvas, 2, 2, width-2, height-2, 15, color)

        # Re-add text (this is simplified - in a real app you'd store the text)
        canvas.create_text(width//2, height//2, text="Button", fill='white',
                          font=('Segoe UI', 11, 'bold'))

    def create_modern_entry(self, parent, placeholder="", show=None, width=300):
        """Create a modern styled entry widget."""
        canvas = tk.Canvas(parent, width=width, height=45,
                          bg=self.colors['bg_primary'], highlightthickness=0)

        # Draw rounded background
        self.draw_rounded_rectangle(canvas, 0, 0, width, 45, 10, self.colors['input_bg'])

        # Create entry widget
        entry = tk.Entry(canvas, bg=self.colors['input_bg'], fg=self.colors['text_primary'],
                        font=('Segoe UI', 11), border=0, insertbackground='white',
                        show=show)

        canvas.create_window(width//2, 22, window=entry, width=width-20)

        # Placeholder functionality
        if placeholder:
            entry.insert(0, placeholder)
            entry.configure(fg=self.colors['text_secondary'])

            def on_focus_in(event):
                if entry.get() == placeholder:
                    entry.delete(0, tk.END)
                    entry.configure(fg=self.colors['text_primary'])

            def on_focus_out(event):
                if not entry.get():
                    entry.insert(0, placeholder)
                    entry.configure(fg=self.colors['text_secondary'])

            entry.bind('<FocusIn>', on_focus_in)
            entry.bind('<FocusOut>', on_focus_out)

        return canvas, entry

    def create_login_screen(self):
        """Create the login/setup screen."""
        # Clear window
        for widget in self.root.winfo_children():
            widget.destroy()

        # Main container
        main_frame = tk.Frame(self.root, bg=self.colors['bg_primary'])
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Center container
        center_frame = tk.Frame(main_frame, bg=self.colors['bg_primary'])
        center_frame.place(relx=0.5, rely=0.5, anchor='center')

        # Logo/Title
        title_label = tk.Label(center_frame, text="🔐 SecureVault",
                              font=('Segoe UI', 32, 'bold'),
                              fg=self.colors['text_primary'], bg=self.colors['bg_primary'])
        title_label.pack(pady=(0, 10))

        subtitle_label = tk.Label(center_frame, text="Your Modern Password Manager",
                                 font=('Segoe UI', 14),
                                 fg=self.colors['text_secondary'], bg=self.colors['bg_primary'])
        subtitle_label.pack(pady=(0, 40))

        # Login card
        card_canvas, card_frame = self.create_rounded_frame(center_frame, 400, 300, 20)
        card_canvas.pack(pady=20)

        # Card content
        card_title = tk.Label(card_frame, text="Master Password",
                             font=('Segoe UI', 18, 'bold'),
                             fg=self.colors['text_primary'], bg=self.colors['bg_card'])
        card_title.pack(pady=(30, 20))

        # Password entry
        self.login_entry_canvas, self.login_entry = self.create_modern_entry(
            card_frame, "Enter your master password", show="*", width=320)
        self.login_entry_canvas.pack(pady=10)

        # Buttons frame
        buttons_frame = tk.Frame(card_frame, bg=self.colors['bg_card'])
        buttons_frame.pack(pady=20)

        # Login button
        login_btn = self.create_modern_button(buttons_frame, "🔓 Unlock Vault",
                                            self.login, width=150, height=40)
        login_btn.pack(side=tk.LEFT, padx=5)

        # Create new vault button
        create_btn = self.create_modern_button(buttons_frame, "✨ Create New",
                                             self.create_new_vault, width=150, height=40,
                                             bg_color=self.colors['success'])
        create_btn.pack(side=tk.LEFT, padx=5)

        # Bind Enter key
        self.login_entry.bind('<Return>', lambda e: self.login())

    def derive_key(self, password: str, salt: bytes = None) -> tuple:
        """Derive encryption key from password using simple hashing."""
        if salt is None:
            salt = os.urandom(16)

        # Simple key derivation using hashlib (fallback if cryptography not available)
        key_material = password.encode() + salt
        for _ in range(100000):  # Simple iteration for key strengthening
            key_material = hashlib.sha256(key_material).digest()

        key = base64.urlsafe_b64encode(key_material)
        return key, salt

    def encrypt_data(self, data: str, key: bytes) -> bytes:
        """Encrypt data using simple XOR cipher (fallback)."""
        try:
            from cryptography.fernet import Fernet
            cipher_suite = Fernet(key)
            return cipher_suite.encrypt(data.encode())
        except ImportError:
            # Simple XOR encryption as fallback
            key_bytes = base64.urlsafe_b64decode(key)
            data_bytes = data.encode()
            encrypted = bytearray()

            for i, byte in enumerate(data_bytes):
                encrypted.append(byte ^ key_bytes[i % len(key_bytes)])

            return base64.b64encode(bytes(encrypted))

    def decrypt_data(self, encrypted_data: bytes, key: bytes) -> str:
        """Decrypt data."""
        try:
            from cryptography.fernet import Fernet
            cipher_suite = Fernet(key)
            return cipher_suite.decrypt(encrypted_data).decode()
        except ImportError:
            # Simple XOR decryption as fallback
            key_bytes = base64.urlsafe_b64decode(key)
            encrypted_bytes = base64.b64decode(encrypted_data)
            decrypted = bytearray()

            for i, byte in enumerate(encrypted_bytes):
                decrypted.append(byte ^ key_bytes[i % len(key_bytes)])

            return bytes(decrypted).decode()

    def login(self):
        """Handle login attempt."""
        password = self.login_entry.get()
        if not password or password == "Enter your master password":
            self.show_message("Please enter your master password", "warning")
            return

        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'rb') as f:
                    data = f.read()

                # Extract salt (first 16 bytes)
                salt = data[:16]
                encrypted_data = data[16:]

                # Derive key
                key, _ = self.derive_key(password, salt)

                # Try to decrypt
                decrypted_data = self.decrypt_data(encrypted_data, key)
                self.passwords = json.loads(decrypted_data)

                self.master_key = key
                self.create_main_interface()

            except Exception as e:
                self.show_message("Invalid master password!", "danger")
        else:
            self.show_message("No vault found. Create a new one!", "warning")

    def create_new_vault(self):
        """Create a new password vault."""
        password = self.login_entry.get()
        if not password or password == "Enter your master password" or len(password) < 6:
            self.show_message("Master password must be at least 6 characters!", "warning")
            return

        # Derive key
        key, salt = self.derive_key(password)

        # Create empty password dictionary
        self.passwords = {}

        # Save encrypted data
        self.save_data(key, salt)

        self.master_key = key
        self.show_message("Vault created successfully!", "success")
        self.create_main_interface()

    def save_data(self, key=None, salt=None):
        """Save encrypted password data."""
        if key is None:
            key = self.master_key

        if salt is None:
            salt = os.urandom(16)

        # Encrypt data
        json_data = json.dumps(self.passwords)
        encrypted_data = self.encrypt_data(json_data, key)

        # Save salt + encrypted data
        with open(self.data_file, 'wb') as f:
            f.write(salt + encrypted_data)

    def show_message(self, message, msg_type="info"):
        """Show a modern message popup."""
        colors = {
            "info": self.colors['accent'],
            "success": self.colors['success'],
            "warning": self.colors['warning'],
            "danger": self.colors['danger']
        }

        # Create popup window
        popup = tk.Toplevel(self.root)
        popup.title("SecureVault")
        popup.geometry("350x150")
        popup.configure(bg=self.colors['bg_primary'])
        popup.resizable(False, False)
        popup.transient(self.root)
        popup.grab_set()

        # Center the popup
        popup.geometry("+%d+%d" % (self.root.winfo_rootx() + 275, self.root.winfo_rooty() + 275))

        # Message content
        canvas, frame = self.create_rounded_frame(popup, 320, 120, 15)
        canvas.pack(pady=15)

        # Icon and message
        icon_map = {"info": "ℹ️", "success": "✅", "warning": "⚠️", "danger": "❌"}
        icon = icon_map.get(msg_type, "ℹ️")

        icon_label = tk.Label(frame, text=icon, font=('Segoe UI', 24),
                             fg=colors[msg_type], bg=self.colors['bg_card'])
        icon_label.pack(pady=(15, 5))

        msg_label = tk.Label(frame, text=message, font=('Segoe UI', 11),
                            fg=self.colors['text_primary'], bg=self.colors['bg_card'],
                            wraplength=280)
        msg_label.pack(pady=(0, 15))

        # Auto close after 2 seconds
        popup.after(2000, popup.destroy)

    def create_main_interface(self):
        """Create the main password manager interface."""
        # Clear window
        for widget in self.root.winfo_children():
            widget.destroy()

        # Main container
        main_frame = tk.Frame(self.root, bg=self.colors['bg_primary'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Header
        header_frame = tk.Frame(main_frame, bg=self.colors['bg_primary'])
        header_frame.pack(fill=tk.X, pady=(0, 20))

        # Title
        title_label = tk.Label(header_frame, text="🔐 SecureVault",
                              font=('Segoe UI', 24, 'bold'),
                              fg=self.colors['text_primary'], bg=self.colors['bg_primary'])
        title_label.pack(side=tk.LEFT)

        # Header buttons
        header_buttons = tk.Frame(header_frame, bg=self.colors['bg_primary'])
        header_buttons.pack(side=tk.RIGHT)

        add_btn = self.create_modern_button(header_buttons, "➕ Add Password",
                                          self.show_add_dialog, width=140, height=35)
        add_btn.pack(side=tk.LEFT, padx=5)

        generate_btn = self.create_modern_button(header_buttons, "🎲 Generate",
                                               self.show_generate_dialog, width=120, height=35,
                                               bg_color=self.colors['success'])
        generate_btn.pack(side=tk.LEFT, padx=5)

        logout_btn = self.create_modern_button(header_buttons, "🚪 Logout",
                                             self.logout, width=100, height=35,
                                             bg_color=self.colors['danger'])
        logout_btn.pack(side=tk.LEFT, padx=5)

        # Search bar
        search_frame = tk.Frame(main_frame, bg=self.colors['bg_primary'])
        search_frame.pack(fill=tk.X, pady=(0, 20))

        search_label = tk.Label(search_frame, text="🔍 Search:",
                               font=('Segoe UI', 12),
                               fg=self.colors['text_primary'], bg=self.colors['bg_primary'])
        search_label.pack(side=tk.LEFT, padx=(0, 10))

        self.search_canvas, self.search_entry = self.create_modern_entry(
            search_frame, "Search passwords...", width=400)
        self.search_canvas.pack(side=tk.LEFT)

        self.search_entry.bind('<KeyRelease>', self.filter_passwords)

        # Passwords container
        self.passwords_frame = tk.Frame(main_frame, bg=self.colors['bg_primary'])
        self.passwords_frame.pack(fill=tk.BOTH, expand=True)

        # Create scrollable area
        self.create_scrollable_passwords_area()

        # Load and display passwords
        self.refresh_password_list()

    def create_scrollable_passwords_area(self):
        """Create scrollable area for password cards."""
        # Canvas for scrolling
        self.canvas = tk.Canvas(self.passwords_frame, bg=self.colors['bg_primary'],
                               highlightthickness=0)
        self.scrollbar = ttk.Scrollbar(self.passwords_frame, orient="vertical",
                                      command=self.canvas.yview)
        self.scrollable_frame = tk.Frame(self.canvas, bg=self.colors['bg_primary'])

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)

        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")

        # Bind mousewheel
        self.canvas.bind("<MouseWheel>", self._on_mousewheel)

    def _on_mousewheel(self, event):
        """Handle mouse wheel scrolling."""
        self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def refresh_password_list(self, filter_text=""):
        """Refresh the password list display."""
        # Clear existing widgets
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

        if not self.passwords:
            # Empty state
            empty_label = tk.Label(self.scrollable_frame,
                                  text="🔒 No passwords saved yet\nClick 'Add Password' to get started!",
                                  font=('Segoe UI', 14),
                                  fg=self.colors['text_secondary'],
                                  bg=self.colors['bg_primary'],
                                  justify=tk.CENTER)
            empty_label.pack(expand=True, pady=100)
            return

        # Filter passwords
        filtered_passwords = {}
        for site, data in self.passwords.items():
            if filter_text.lower() in site.lower() or filter_text.lower() in data.get('username', '').lower():
                filtered_passwords[site] = data

        # Create password cards
        for i, (site, data) in enumerate(filtered_passwords.items()):
            self.create_password_card(site, data, i)

    def create_password_card(self, site, data, index):
        """Create a modern password card."""
        # Card container
        card_container = tk.Frame(self.scrollable_frame, bg=self.colors['bg_primary'])
        card_container.pack(fill=tk.X, pady=10, padx=10)

        # Card canvas
        card_canvas, card_frame = self.create_rounded_frame(card_container, 840, 100, 15)
        card_canvas.pack()

        # Card content
        content_frame = tk.Frame(card_frame, bg=self.colors['bg_card'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=15)

        # Left side - Info
        info_frame = tk.Frame(content_frame, bg=self.colors['bg_card'])
        info_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Site name
        site_label = tk.Label(info_frame, text=f"🌐 {site}",
                             font=('Segoe UI', 14, 'bold'),
                             fg=self.colors['text_primary'], bg=self.colors['bg_card'])
        site_label.pack(anchor='w')

        # Username
        username = data.get('username', 'N/A')
        username_label = tk.Label(info_frame, text=f"👤 {username}",
                                 font=('Segoe UI', 10),
                                 fg=self.colors['text_secondary'], bg=self.colors['bg_card'])
        username_label.pack(anchor='w', pady=(2, 0))

        # Password (masked)
        password_text = "•" * min(len(data.get('password', '')), 12)
        password_label = tk.Label(info_frame, text=f"🔑 {password_text}",
                                 font=('Segoe UI', 10),
                                 fg=self.colors['text_secondary'], bg=self.colors['bg_card'])
        password_label.pack(anchor='w', pady=(2, 0))

        # Right side - Actions
        actions_frame = tk.Frame(content_frame, bg=self.colors['bg_card'])
        actions_frame.pack(side=tk.RIGHT)

        # Action buttons
        copy_btn = self.create_modern_button(actions_frame, "📋",
                                           lambda s=site: self.copy_password(s),
                                           width=40, height=35, font_size=12)
        copy_btn.pack(side=tk.LEFT, padx=2)

        edit_btn = self.create_modern_button(actions_frame, "✏️",
                                           lambda s=site: self.edit_password(s),
                                           width=40, height=35, font_size=12,
                                           bg_color=self.colors['warning'])
        edit_btn.pack(side=tk.LEFT, padx=2)

        delete_btn = self.create_modern_button(actions_frame, "🗑️",
                                             lambda s=site: self.delete_password(s),
                                             width=40, height=35, font_size=12,
                                             bg_color=self.colors['danger'])
        delete_btn.pack(side=tk.LEFT, padx=2)

    def filter_passwords(self, event=None):
        """Filter passwords based on search text."""
        search_text = self.search_entry.get()
        if search_text == "Search passwords...":
            search_text = ""
        self.refresh_password_list(search_text)

    def copy_password(self, site):
        """Copy password to clipboard."""
        if site in self.passwords:
            password = self.passwords[site]['password']
            self.root.clipboard_clear()
            self.root.clipboard_append(password)
            self.show_message(f"Password for {site} copied!", "success")

    def delete_password(self, site):
        """Delete a password entry."""
        if site in self.passwords:
            # Create confirmation dialog
            result = messagebox.askyesno("Confirm Delete",
                                       f"Are you sure you want to delete the password for {site}?")
            if result:
                del self.passwords[site]
                self.save_data()
                self.refresh_password_list()
                self.show_message(f"Password for {site} deleted!", "success")

    def show_add_dialog(self):
        """Show add password dialog."""
        self.show_password_dialog()

    def edit_password(self, site):
        """Edit an existing password."""
        if site in self.passwords:
            data = self.passwords[site]
            self.show_password_dialog(site, data['username'], data['password'])

    def show_password_dialog(self, site="", username="", password=""):
        """Show add/edit password dialog."""
        # Create dialog window
        dialog = tk.Toplevel(self.root)
        dialog.title("Add Password" if not site else "Edit Password")
        dialog.geometry("450x400")
        dialog.configure(bg=self.colors['bg_primary'])
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # Center dialog
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 225, self.root.winfo_rooty() + 150))

        # Dialog content
        content_canvas, content_frame = self.create_rounded_frame(dialog, 420, 370, 20)
        content_canvas.pack(pady=15)

        # Title
        title_text = "Add New Password" if not site else f"Edit Password for {site}"
        title_label = tk.Label(content_frame, text=title_text,
                              font=('Segoe UI', 16, 'bold'),
                              fg=self.colors['text_primary'], bg=self.colors['bg_card'])
        title_label.pack(pady=(20, 30))

        # Form fields
        # Site/Service
        site_label = tk.Label(content_frame, text="Website/Service:",
                             font=('Segoe UI', 11),
                             fg=self.colors['text_primary'], bg=self.colors['bg_card'])
        site_label.pack(anchor='w', padx=30, pady=(0, 5))

        site_canvas, site_entry = self.create_modern_entry(content_frame, "e.g., Gmail, Facebook", width=360)
        site_canvas.pack(pady=(0, 15))
        if site:
            site_entry.delete(0, tk.END)
            site_entry.insert(0, site)
            site_entry.configure(fg=self.colors['text_primary'])

        # Username
        username_label = tk.Label(content_frame, text="Username/Email:",
                                 font=('Segoe UI', 11),
                                 fg=self.colors['text_primary'], bg=self.colors['bg_card'])
        username_label.pack(anchor='w', padx=30, pady=(0, 5))

        username_canvas, username_entry = self.create_modern_entry(content_frame, "<EMAIL>", width=360)
        username_canvas.pack(pady=(0, 15))
        if username:
            username_entry.delete(0, tk.END)
            username_entry.insert(0, username)
            username_entry.configure(fg=self.colors['text_primary'])

        # Password
        password_label = tk.Label(content_frame, text="Password:",
                                 font=('Segoe UI', 11),
                                 fg=self.colors['text_primary'], bg=self.colors['bg_card'])
        password_label.pack(anchor='w', padx=30, pady=(0, 5))

        password_canvas, password_entry = self.create_modern_entry(content_frame, "Enter password",
                                                                  show="*", width=360)
        password_canvas.pack(pady=(0, 20))
        if password:
            password_entry.delete(0, tk.END)
            password_entry.insert(0, password)
            password_entry.configure(fg=self.colors['text_primary'])

        # Buttons
        buttons_frame = tk.Frame(content_frame, bg=self.colors['bg_card'])
        buttons_frame.pack(pady=10)

        def save_password():
            site_val = site_entry.get().strip()
            username_val = username_entry.get().strip()
            password_val = password_entry.get().strip()

            if not site_val or site_val == "e.g., Gmail, Facebook":
                self.show_message("Please enter a website/service name!", "warning")
                return

            if not username_val or username_val == "<EMAIL>":
                self.show_message("Please enter a username/email!", "warning")
                return

            if not password_val or password_val == "Enter password":
                self.show_message("Please enter a password!", "warning")
                return

            # Save password
            self.passwords[site_val] = {
                'username': username_val,
                'password': password_val
            }

            self.save_data()
            self.refresh_password_list()

            action = "updated" if site else "added"
            self.show_message(f"Password {action} successfully!", "success")
            dialog.destroy()

        save_btn = self.create_modern_button(buttons_frame, "💾 Save", save_password,
                                           width=120, height=40)
        save_btn.pack(side=tk.LEFT, padx=5)

        cancel_btn = self.create_modern_button(buttons_frame, "❌ Cancel", dialog.destroy,
                                             width=120, height=40, bg_color=self.colors['danger'])
        cancel_btn.pack(side=tk.LEFT, padx=5)

    def show_generate_dialog(self):
        """Show password generator dialog."""
        # Create dialog
        dialog = tk.Toplevel(self.root)
        dialog.title("Password Generator")
        dialog.geometry("400x350")
        dialog.configure(bg=self.colors['bg_primary'])
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # Center dialog
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 250, self.root.winfo_rooty() + 175))

        # Dialog content
        content_canvas, content_frame = self.create_rounded_frame(dialog, 370, 320, 20)
        content_canvas.pack(pady=15)

        # Title
        title_label = tk.Label(content_frame, text="🎲 Password Generator",
                              font=('Segoe UI', 16, 'bold'),
                              fg=self.colors['text_primary'], bg=self.colors['bg_card'])
        title_label.pack(pady=(20, 20))

        # Length slider
        length_label = tk.Label(content_frame, text="Password Length: 12",
                               font=('Segoe UI', 11),
                               fg=self.colors['text_primary'], bg=self.colors['bg_card'])
        length_label.pack(pady=(0, 10))

        length_var = tk.IntVar(value=12)
        length_scale = tk.Scale(content_frame, from_=8, to=32, orient=tk.HORIZONTAL,
                               variable=length_var, bg=self.colors['bg_card'],
                               fg=self.colors['text_primary'], highlightthickness=0,
                               command=lambda v: length_label.config(text=f"Password Length: {v}"))
        length_scale.pack(pady=(0, 15))

        # Options
        options_frame = tk.Frame(content_frame, bg=self.colors['bg_card'])
        options_frame.pack(pady=10)

        uppercase_var = tk.BooleanVar(value=True)
        lowercase_var = tk.BooleanVar(value=True)
        numbers_var = tk.BooleanVar(value=True)
        symbols_var = tk.BooleanVar(value=True)

        tk.Checkbutton(options_frame, text="Uppercase (A-Z)", variable=uppercase_var,
                      bg=self.colors['bg_card'], fg=self.colors['text_primary'],
                      selectcolor=self.colors['accent']).pack(anchor='w')
        tk.Checkbutton(options_frame, text="Lowercase (a-z)", variable=lowercase_var,
                      bg=self.colors['bg_card'], fg=self.colors['text_primary'],
                      selectcolor=self.colors['accent']).pack(anchor='w')
        tk.Checkbutton(options_frame, text="Numbers (0-9)", variable=numbers_var,
                      bg=self.colors['bg_card'], fg=self.colors['text_primary'],
                      selectcolor=self.colors['accent']).pack(anchor='w')
        tk.Checkbutton(options_frame, text="Symbols (!@#$)", variable=symbols_var,
                      bg=self.colors['bg_card'], fg=self.colors['text_primary'],
                      selectcolor=self.colors['accent']).pack(anchor='w')

        # Generated password display
        password_canvas, password_display = self.create_modern_entry(content_frame,
                                                                    "Generated password will appear here",
                                                                    width=340)
        password_canvas.pack(pady=15)

        def generate_password():
            chars = ""
            if uppercase_var.get():
                chars += string.ascii_uppercase
            if lowercase_var.get():
                chars += string.ascii_lowercase
            if numbers_var.get():
                chars += string.digits
            if symbols_var.get():
                chars += "!@#$%^&*"

            if not chars:
                self.show_message("Please select at least one character type!", "warning")
                return

            password = ''.join(secrets.choice(chars) for _ in range(length_var.get()))
            password_display.delete(0, tk.END)
            password_display.insert(0, password)
            password_display.configure(fg=self.colors['text_primary'])

        def copy_generated():
            password = password_display.get()
            if password and password != "Generated password will appear here":
                self.root.clipboard_clear()
                self.root.clipboard_append(password)
                self.show_message("Password copied to clipboard!", "success")

        # Buttons
        buttons_frame = tk.Frame(content_frame, bg=self.colors['bg_card'])
        buttons_frame.pack(pady=15)

        generate_btn = self.create_modern_button(buttons_frame, "🎲 Generate", generate_password,
                                               width=100, height=35)
        generate_btn.pack(side=tk.LEFT, padx=5)

        copy_btn = self.create_modern_button(buttons_frame, "📋 Copy", copy_generated,
                                           width=100, height=35, bg_color=self.colors['success'])
        copy_btn.pack(side=tk.LEFT, padx=5)

    def logout(self):
        """Logout and return to login screen."""
        self.passwords = {}
        self.master_key = None
        self.create_login_screen()

    def run(self):
        """Start the application."""
        self.root.mainloop()

def main():
    """Main function to run the password manager."""
    try:
        app = ModernPasswordManager()
        app.run()
    except Exception as e:
        print(f"Error running the password manager: {e}")
        print("Make sure you have tkinter installed (usually comes with Python)")

if __name__ == "__main__":
    main()