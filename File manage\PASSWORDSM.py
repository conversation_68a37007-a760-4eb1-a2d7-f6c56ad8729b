import hashlib
import getpass

password_manager = {}

def create_account():
    username = input("Enter the username: ")
    password = getpass.getpass("Enter the password: ")
    hashed_password = hashlib.sha256(password.encode()).hexdigest()
    password_manager[username] = hashed_password
    print ("Account created successfully.")

def login():
    username = input("enter your username : ")
    print (f"{password_manager[username]}")
    hashed_password = hashlib.sha256(password.encode()).hexdigest()
    if username in password_manager and password_manager[username] == hashed_password:
        print("Login successful.")
    else:
        print("Invalid username or password.")

while True:
    print("\n1. Create Account")
    print("2. Login")
    print("3. Exit")
    choice = input("Enter your choice: ")

    if choice == "1":
        create_account()
    elif choice == "2":
        login()
    elif choice == "3":
        break
    else:
        print("Invalid choice. Please try again.")

if __name__ == "__main__":
    main()