import requests
import json
from datetime import datetime, timed<PERSON><PERSON>

def get_ipl_scores():
    # You'll need to register for an API key at cricapi.com or similar service
    API_KEY = "YOUR_API_KEY_HERE"
    
    # Get yesterday's date in required format
    yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
    
    # API endpoint for cricket matches
    url = f"https://api.cricapi.com/v1/matches?apikey={API_KEY}&date={yesterday}"
    
    try:
        response = requests.get(url)
        data = response.json()
        
        # Filter for IPL matches only
        ipl_matches = [match for match in data.get('data', []) 
                      if 'Indian Premier League' in match.get('name', '')]
        
        if not ipl_matches:
            return "No IPL matches scheduled for yesterday."
        
        # Display match information
        results = []
        for match in ipl_matches:
            match_info = f"{match.get('name')}: {match.get('status')}"
            results.append(match_info)
            
        return "\n".join(results)
    
    except Exception as e:
        return f"Error fetching IPL scores: {str(e)}"

if __name__ == "__main__":
    print(get_ipl_scores())

