<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Catch the Star</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: 'Arial', sans-serif;
            touch-action: manipulation;
        }
        
        #game-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            background: linear-gradient(to bottom, #1e528e 0%, #728a7c 50%, #e9e9e9 100%);
        }
        
        #ui {
            position: absolute;
            top: 20px;
            width: 100%;
            display: flex;
            justify-content: space-between;
            padding: 0 20px;
            box-sizing: border-box;
            color: white;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
            font-size: 24px;
            z-index: 100;
        }
        
        #score, #lives {
            background-color: rgba(0, 0, 0, 0.3);
            padding: 5px 15px;
            border-radius: 10px;
        }
        
        #bucket {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 50px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 80 50"><path d="M5,10 L75,10 L70,40 L10,40 Z" fill="%23d4a76a" stroke="%23996633" stroke-width="2"/><path d="M5,10 L75,10 L70,40 L10,40 Z" fill="%23d4a76a" stroke="%23996633" stroke-width="2" transform="translate(0 2)" opacity="0.2"/></svg>');
            background-size: contain;
            background-repeat: no-repeat;
            filter: drop-shadow(2px 4px 3px rgba(0, 0, 0, 0.3));
            z-index: 10;
            transition: transform 0.1s ease-out;
        }
        
        .star {
            position: absolute;
            width: 30px;
            height: 30px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30"><path d="M15,2 L18,10 L26,10 L20,16 L23,24 L15,19 L7,24 L10,16 L4,10 L12,10 Z" fill="%23FFD700"/></svg>');
            background-size: contain;
            z-index: 5;
            animation: sparkle 1.5s infinite alternate;
        }
        
        .cloud {
            position: absolute;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 50"><path d="M25,25 Q15,10 5,25 Q-5,40 15,40 Q25,55 40,40 Q55,55 70,40 Q85,50 95,35 Q85,20 70,25 Q60,5 40,15 Q30,5 25,25 Z" fill="%23ffffff" opacity="0.8"/></svg>');
            background-size: contain;
            background-repeat: no-repeat;
            opacity: 0.8;
            z-index: 1;
        }
        
        @keyframes sparkle {
            0% { transform: scale(1) rotate(0deg); opacity: 0.8; }
            100% { transform: scale(1.2) rotate(10deg); opacity: 1; }
        }
        
        #game-over {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 200;
            display: none;
        }
        
        #game-over h1 {
            font-size: 48px;
            margin-bottom: 20px;
        }
        
        #restart-btn {
            padding: 10px 20px;
            font-size: 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        #restart-btn:hover {
            background-color: #45a049;
        }
        
        #mobile-controls {
            position: absolute;
            bottom: 80px;
            width: 100%;
            display: flex;
            justify-content: space-between;
            padding: 0 20px;
            box-sizing: border-box;
            display: none;
        }
        
        .mobile-btn {
            width: 80px;
            height: 80px;
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 30px;
            color: white;
            user-select: none;
            -webkit-user-select: none;
        }
        
        @media (max-width: 768px) {
            #mobile-controls {
                display: flex;
            }
        }
    </style>
</head>
<body>
    <div id="game-container">
        <div id="ui">
            <div id="score">Score: 0</div>
            <div id="lives">Lives: 3</div>
        </div>
        
        <div id="bucket"></div>
        
        <div id="mobile-controls">
            <div class="mobile-btn" id="left-btn">←</div>
            <div class="mobile-btn" id="right-btn">→</div>
        </div>
        
        <div id="game-over">
            <h1>Game Over</h1>
            <p id="final-score">Your score: 0</p>
            <button id="restart-btn">Play Again</button>
        </div>
    </div>
    
    <audio id="catch-sound" src="data:audio/wav;base64,UklGRl9vT19XQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YU..."></audio>
    <audio id="bg-music" src="data:audio/mpeg;base64,SUQzBAAAAAABEVRYWFgAAAAtAAADY29tbWVudABCaWdTb3VuZEJhbmsuY29tIC8gTGFTb25vdGhlc..." loop></audio>

    <script>
        // Game variables
        let score = 0;
        let lives = 3;
        let gameRunning = true;
        let bucketX = 0;
        let targetX = 0;
        let stars = [];
        let clouds = [];
        let lastStarTime = 0;
        let starInterval = 1000; // milliseconds between stars
        let starSpeed = 2; // base speed
        let speedIncrease = 0.05; // speed increase per star
        
        // DOM elements
        const gameContainer = document.getElementById('game-container');
        const bucket = document.getElementById('bucket');
        const scoreDisplay = document.getElementById('score');
        const livesDisplay = document.getElementById('lives');
        const gameOverScreen = document.getElementById('game-over');
        const finalScoreDisplay = document.getElementById('final-score');
        const restartBtn = document.getElementById('restart-btn');
        const catchSound = document.getElementById('catch-sound');
        const bgMusic = document.getElementById('bg-music');
        const leftBtn = document.getElementById('left-btn');
        const rightBtn = document.getElementById('right-btn');
        
        // Initialize game
        function init() {
            // Set initial bucket position
            bucketX = gameContainer.clientWidth / 2;
            updateBucketPosition();
            
            // Create initial clouds
            for (let i = 0; i < 5; i++) {
                createCloud();
            }
            
            // Start game loop
            requestAnimationFrame(gameLoop);
            
            // Start background music (with user interaction requirement)
            document.addEventListener('click', startMusicOnce, { once: true });
            
            // Event listeners
            window.addEventListener('resize', handleResize);
            document.addEventListener('keydown', handleKeyDown);
            document.addEventListener('keyup', handleKeyUp);
            restartBtn.addEventListener('click', restartGame);
            
            // Mobile controls
            leftBtn.addEventListener('touchstart', () => moveLeft = true);
            leftBtn.addEventListener('touchend', () => moveLeft = false);
            leftBtn.addEventListener('touchcancel', () => moveLeft = false);
            rightBtn.addEventListener('touchstart', () => moveRight = true);
            rightBtn.addEventListener('touchend', () => moveRight = false);
            rightBtn.addEventListener('touchcancel', () => moveRight = false);
            
            // Touch controls for bucket movement
            gameContainer.addEventListener('touchmove', handleTouchMove, { passive: false });
        }
        
        // Keyboard controls
        let moveLeft = false;
        let moveRight = false;
        
        function handleKeyDown(e) {
            if (e.key === 'ArrowLeft' || e.key === 'a' || e.key === 'A') {
                moveLeft = true;
            } else if (e.key === 'ArrowRight' || e.key === 'd' || e.key === 'D') {
                moveRight = true;
            }
        }
        
        function handleKeyUp(e) {
            if (e.key === 'ArrowLeft' || e.key === 'a' || e.key === 'A') {
                moveLeft = false;
            } else if (e.key === 'ArrowRight' || e.key === 'd' || e.key === 'D') {
                moveRight = false;
            }
        }
        
        // Touch controls
        function handleTouchMove(e) {
            e.preventDefault();
            if (e.touches.length > 0) {
                const touchX = e.touches[0].clientX;
                targetX = touchX;
            }
        }
        
        // Start music after user interaction
        function startMusicOnce() {
            bgMusic.volume = 0.3;
            bgMusic.play().catch(e => console.log("Audio play failed:", e));
        }
        
        // Handle window resize
        function handleResize() {
            // Keep bucket within bounds
            const maxX = gameContainer.clientWidth - bucket.clientWidth;
            if (bucketX > maxX) {
                bucketX = maxX;
                updateBucketPosition();
            }
        }
        
        // Create a new star
        function createStar() {
            const star = document.createElement('div');
            star.className = 'star';
            
            // Random x position at top of screen
            const x = Math.random() * (gameContainer.clientWidth - 30);
            star.style.left = `${x}px`;
            star.style.top = '-30px';
            
            gameContainer.appendChild(star);
            
            stars.push({
                element: star,
                x: x,
                y: -30,
                speed: starSpeed + (Math.random() * 2),
                rotation: Math.random() * 360,
                rotationSpeed: (Math.random() - 0.5) * 4
            });
        }
        
        // Create a cloud
        function createCloud() {
            const cloud = document.createElement('div');
            cloud.className = 'cloud';
            
            // Random size and position
            const width = 100 + Math.random() * 150;
            const height = width * 0.5;
            const x = Math.random() * gameContainer.clientWidth;
            const y = Math.random() * (gameContainer.clientHeight * 0.6);
            const speed = 0.2 + Math.random() * 0.5;
            
            cloud.style.width = `${width}px`;
            cloud.style.height = `${height}px`;
            cloud.style.left = `${x}px`;
            cloud.style.top = `${y}px`;
            
            gameContainer.appendChild(cloud);
            
            clouds.push({
                element: cloud,
                x: x,
                y: y,
                width: width,
                speed: speed
            });
        }
        
        // Update bucket position with easing
        function updateBucketPosition() {
            // Handle keyboard/touch input
            if (moveLeft) targetX -= 10;
            if (moveRight) targetX += 10;
            
            // Apply easing to movement
            bucketX += (targetX - bucketX) * 0.2;
            
            // Keep within bounds
            const maxX = gameContainer.clientWidth - bucket.clientWidth;
            if (bucketX < 0) bucketX = 0;
            if (bucketX > maxX) bucketX = maxX;
            
            bucket.style.left = `${bucketX}px`;
        }
        
        // Update stars
        function updateStars() {
            const now = Date.now();
            if (now - lastStarTime > starInterval && gameRunning) {
                createStar();
                lastStarTime = now;
                
                // Gradually decrease interval to increase difficulty
                starInterval = Math.max(300, starInterval - 5);
            }
            
            for (let i = stars.length - 1; i >= 0; i--) {
                const star = stars[i];
                
                // Update position
                star.y += star.speed;
                star.rotation += star.rotationSpeed;
                
                star.element.style.top = `${star.y}px`;
                star.element.style.transform = `rotate(${star.rotation}deg)`;
                
                // Check if caught by bucket
                if (star.y + 30 >= gameContainer.clientHeight - 70) {
                    if (star.x + 15 >= bucketX && star.x + 15 <= bucketX + 80) {
                        // Caught!
                        catchStar(i);
                        continue;
                    }
                }
                
                // Check if missed (fell off screen)
                if (star.y > gameContainer.clientHeight) {
                    missStar(i);
                }
            }
        }
        
        // Update clouds
        function updateClouds() {
            for (let i = clouds.length - 1; i >= 0; i--) {
                const cloud = clouds[i];
                
                // Move cloud
                cloud.x += cloud.speed;
                
                // If cloud moves off screen, remove it and maybe create a new one
                if (cloud.x > gameContainer.clientWidth + cloud.width) {
                    gameContainer.removeChild(cloud.element);
                    clouds.splice(i, 1);
                    
                    // 50% chance to create a new cloud
                    if (Math.random() > 0.5) {
                        createCloud();
                    }
                } else {
                    cloud.element.style.left = `${cloud.x}px`;
                }
            }
        }
        
        // Handle catching a star
        function catchStar(index) {
            const star = stars[index];
            
            // Play sound
            catchSound.currentTime = 0;
            catchSound.play();
            
            // Remove star
            gameContainer.removeChild(star.element);
            stars.splice(index, 1);
            
            // Increase score
            score++;
            scoreDisplay.textContent = `Score: ${score}`;
            
            // Slightly increase speed for next stars
            starSpeed += speedIncrease;
            
            // Add visual feedback
            bucket.style.transform = 'translateX(-50%) scale(1.1)';
            setTimeout(() => {
                bucket.style.transform = 'translateX(-50%) scale(1)';
            }, 100);
        }
        
        // Handle missing a star
        function missStar(index) {
            const star = stars[index];
            
            // Remove star
            gameContainer.removeChild(star.element);
            stars.splice(index, 1);
            
            // Decrease lives
            lives--;
            livesDisplay.textContent = `Lives: ${lives}`;
            
            // Check for game over
            if (lives <= 0) {
                gameOver();
            }
            
            // Add visual feedback
            bucket.style.transform = 'translateX(-50%) rotate(-5deg)';
            setTimeout(() => {
                bucket.style.transform = 'translateX(-50%) rotate(0deg)';
            }, 100);
        }
        
        // Game over
        function gameOver() {
            gameRunning = false;
            finalScoreDisplay.textContent = `Your score: ${score}`;
            gameOverScreen.style.display = 'flex';
        }
        
        // Restart game
        function restartGame() {
            // Clear all stars
            stars.forEach(star => {
                gameContainer.removeChild(star.element);
            });
            stars = [];
            
            // Reset variables
            score = 0;
            lives = 3;
            gameRunning = true;
            starSpeed = 2;
            starInterval = 1000;
            
            // Update UI
            scoreDisplay.textContent = `Score: ${score}`;
            livesDisplay.textContent = `Lives: ${lives}`;
            gameOverScreen.style.display = 'none';
            
            // Reset bucket position
            bucketX = gameContainer.clientWidth / 2;
            targetX = bucketX;
            updateBucketPosition();
        }
        
        // Main game loop
        function gameLoop() {
            if (gameRunning) {
                updateBucketPosition();
                updateStars();
                updateClouds();
            }
            
            requestAnimationFrame(gameLoop);
        }
        
        // Start the game
        init();
    </script>
</body>
</html>