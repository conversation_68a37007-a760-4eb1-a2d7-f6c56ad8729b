<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>Catch the Star</title>
  <style>
    :root{
      --ui-bg: rgba(255,255,255,0.06);
      --ui-text: #ffffff;
      --accent: #ffd54a;
      --font: 'Inter', system-ui, -apple-system, 'Segoe UI', Roboto, 'Helvetica Neue', Arial;
    }
    html,body{height:100%;margin:0;background:#0b1220;display:flex;align-items:center;justify-content:center}
    #gameWrap{width:100%;max-width:900px;height:600px;position:relative;border-radius:12px;overflow:hidden;box-shadow:0 10px 30px rgba(2,6,23,0.6);}
    canvas{display:block;width:100%;height:100%;background:linear-gradient(180deg,#6ec1ff 0%, #87cefa 50%, #ccefff 100%);} 

    .hud{position:absolute;left:12px;top:12px;padding:8px 12px;border-radius:999px;background:var(--ui-bg);backdrop-filter: blur(6px);display:flex;gap:12px;align-items:center;font-family:var(--font);color:var(--ui-text);font-weight:600}
    .hud .item{display:flex;flex-direction:column;align-items:center}
    .hud .label{font-size:11px;opacity:0.85}
    .hud .value{font-size:20px}

    .controls{position:absolute;right:12px;top:12px;padding:8px 12px;border-radius:12px;background:var(--ui-bg);display:flex;gap:8px;align-items:center}
    .btn{padding:6px 10px;border-radius:8px;border:0;background:transparent;color:var(--font);cursor:pointer;font-family:var(--font);color:var(--ui-text);}

    .touchZones{position:absolute;left:0;right:0;bottom:0;height:140px;display:flex;pointer-events:none}
    .touch{flex:1;pointer-events:auto}
    .touch.left{background:linear-gradient(90deg,rgba(0,0,0,0.12),transparent)}
    .touch.right{background:linear-gradient(270deg,rgba(0,0,0,0.06),transparent)}

    .msg{position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);padding:16px 20px;background:rgba(0,0,0,0.28);color:#fff;border-radius:12px;font-family:var(--font);display:none}

    @media (max-width:520px){
      #gameWrap{height:480px}
      .hud .value{font-size:18px}
    }
  </style>
</head>
<body>
  <div id="gameWrap">
    <canvas id="game"></canvas>

    <div class="hud" id="hud">
      <div class="item"><div class="label">Score</div><div class="value" id="score">0</div></div>
      <div class="item"><div class="label">Lives</div><div class="value" id="lives">3</div></div>
    </div>

    <div class="controls">
      <button class="btn" id="muteBtn">🔊</button>
      <button class="btn" id="restartBtn">↻ Restart</button>
    </div>

    <div class="touchZones">
      <div class="touch left" id="leftTouch" aria-hidden="true"></div>
      <div class="touch right" id="rightTouch" aria-hidden="true"></div>
    </div>

    <div class="msg" id="gameOverMsg">Game Over — Tap ↻ to restart</div>
  </div>

<script>
const clamp = (v, a, b) => Math.max(a, Math.min(b, v));
const lerp = (a, b, t) => a + (b - a) * t;

const canvas = document.getElementById('game');
const ctx = canvas.getContext('2d', { alpha: false });
let DPR = Math.min(window.devicePixelRatio || 1, 2);
function resize() {
  const rect = canvas.getBoundingClientRect();
  canvas.width = Math.floor(rect.width * DPR);
  canvas.height = Math.floor(rect.height * DPR);
  ctx.setTransform(DPR, 0, 0, DPR, 0, 0);
}
window.addEventListener('resize', () => { DPR = Math.min(window.devicePixelRatio || 1, 2); resize(); updateGameDims(); });
resize();

const GAME = {
  width: canvas.width / DPR,
  height: canvas.height / DPR,
  gravity: 600,
  spawnInterval: 0.8,
  maxStars: 18,
};
function updateGameDims(){ GAME.width = canvas.width / DPR; GAME.height = canvas.height / DPR; }

const svgBucket = (w=220,h=140) => `data:image/svg+xml;utf8,${encodeURIComponent(`
<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 220 140' width='${w}' height='${h}'>
  <defs>
    <linearGradient id='g' x1='0' x2='0' y1='0' y2='1'>
      <stop offset='0' stop-color='%23ffd97a'/>
      <stop offset='1' stop-color='%23ffb74d'/>
    </linearGradient>
    <filter id='s' x='-50%' y='-50%' width='200%' height='200%'>
      <feDropShadow dx='0' dy='8' stdDeviation='10' flood-color='%23000000' flood-opacity='0.35'/>
    </filter>
  </defs>
  <g filter='url(#s)'>
    <path d='M12 28 C28 8, 192 8, 208 28 L188 118 C188 118,32 118,12 118 Z' fill='url(#g)' stroke='%23885d2a' stroke-width='4' />
  </g>
  <rect x='42' y='46' width='136' height='42' rx='14' fill='%23fff3e0' opacity='0.12'/>
</svg>
`)};

const svgStar = (size=64) => `data:image/svg+xml;utf8,${encodeURIComponent(`
<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 64 64' width='${size}' height='${size}'>
  <defs>
    <radialGradient id='r' cx='30%' cy='30%'>
      <stop offset='0' stop-color='%23fff9c4' />
      <stop offset='1' stop-color='%23ffd54a' />
    </radialGradient>
    <filter id='glow' x='-50%' y='-50%' width='200%' height='200%'>
      <feGaussianBlur stdDeviation='3' result='b' />
      <feMerge><feMergeNode in='b'/><feMergeNode in='SourceGraphic'/></feMerge>
    </filter>
  </defs>
  <g filter='url(#glow)'>
    <path d='M32 6 L38 24 L58 24 L42 36 L48 56 L32 44 L16 56 L22 36 L6 24 L26 24 Z' fill='url(#r)' stroke='%23ffecb3' stroke-width='1.6'/>
  </g>
</svg>
`)}`;

const svgCloud = (w=220,h=120) => `data:image/svg+xml;utf8,${encodeURIComponent(`
<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 220 120' width='${w}' height='${h}'>
  <defs>
    <linearGradient id='cg' x1='0' x2='0' y1='0' y2='1'>
      <stop offset='0' stop-color='%23ffffff' stop-opacity='0.95'/>
      <stop offset='1' stop-color='%23e6f2ff' stop-opacity='0.9'/>
    </linearGradient>
  </defs>
  <g opacity='0.92'>
    <ellipse cx='60' cy='58' rx='50' ry='30' fill='url(#cg)' />
    <ellipse cx='120' cy='50' rx='60' ry='34' fill='url(#cg)' />
    <ellipse cx='170' cy='62' rx='36' ry='22' fill='url(#cg)' />
  </g>
</svg>
`)}`;

function loadImage(src){return new Promise((res,rej)=>{const img=new Image();img.onload=()=>res(img);img.onerror=rej;img.src=src;});}
let ASSETS = {};
async function loadAssets(){
  ASSETS.bucket = await loadImage(svgBucket(300,180));
  ASSETS.star = await loadImage(svgStar(96));
  ASSETS.cloud = await loadImage(svgCloud(320,160));
}

let audioCtx, bgGain, isMuted = false;
function initAudio(){
  try{
    audioCtx = new (window.AudioContext || window.webkitAudioContext)();
    bgGain = audioCtx.createGain(); bgGain.gain.value = 0.06; bgGain.connect(audioCtx.destination);
    const pattern = [0,4,7,12];
    let t0 = audioCtx.currentTime;
    function schedule(){
      for(let i=0;i<pattern.length;i++){
        const o = audioCtx.createOscillator();
        const g = audioCtx.createGain();
        o.type = 'sine';
        o.frequency.value = 220 * Math.pow(2, pattern[i]/12);
        g.gain.value = 0.003;
        o.connect(g); g.connect(bgGain);
        o.start(t0 + i*0.45);
        o.stop(t0 + i*0.45 + 0.35);
      }
      t0 += pattern.length*0.45;
      setTimeout(schedule, Math.max(1, (pattern.length*0.45-0.2)*1000));
    }
    schedule();
  }catch(e){console.warn('Audio init failed',e)}
}

function playCatchSound(){
  if(!audioCtx) return;
  const now = audioCtx.currentTime;
  const o1 = audioCtx.createOscillator();
  const o2 = audioCtx.createOscillator();
  const g = audioCtx.createGain();
  const f = 880;
  o1.type='sine'; o2.type='triangle';
  o1.frequency.setValueAtTime(f,now);
  o2.frequency.setValueAtTime(f*1.5,now);
  g.gain.setValueAtTime(0.0001, now);
  g.gain.exponentialRampToValueAtTime(0.09, now+0.006);
  g.gain.exponentialRampToValueAtTime(0.0001, now+0.45);
  o1.connect(g); o2.connect(g); g.connect(audioCtx.destination);
  o1.start(now); o2.start(now);
  o1.stop(now+0.45); o2.stop(now+0.45);
}

const muteBtn = document.getElementById('muteBtn');
muteBtn.addEventListener('click', ()=>{
  isMuted = !isMuted; muteBtn.textContent = isMuted? '🔇' : '🔊';
  if(audioCtx){
    if(isMuted) audioCtx.suspend(); else audioCtx.resume();
  }
});

const bucket = {
  x: 0, y: 0, width: 240, height: 120, targetX: 0, speed: 1600,
  draw(ctx, img){
    const bw = this.width, bh = this.height;
    ctx.save();
    ctx.globalAlpha = 0.28;
    ctx.beginPath(); ctx.ellipse(this.x + bw/2, this.y + bh - 12, bw*0.36, 12, 0, 0, Math.PI*2); ctx.fillStyle = 'rgba(5,7,10,0.55)'; ctx.fill();
    ctx.globalAlpha = 1;
    ctx.drawImage(img, this.x, this.y, bw, bh);
    ctx.restore();
  },
};

function Star(){ this.reset(); this.active = false; }
Star.prototype.reset = function(){
  this.size = lerp(36, 70, Math.random());
  this.x = Math.random() * (GAME.width - this.size - 40) + 20;
  this.y = -this.size - (Math.random()*80);
  this.vy = lerp(120, 420, Math.random());
  this.rotation = (Math.random()-0.5)*0.8;
  this.spin = (Math.random()-0.5)*3;
  this.collected = false;
  this.sparkPhase = Math.random()*Math.PI*2;
}
Star.prototype.update = function(dt){
  this.vy += GAME.gravity * dt;
  this.y += this.vy * dt;
  this.rotation += this.spin * dt;
}
Star.prototype.draw = function(ctx, img){
  ctx.save();
  ctx.translate(this.x + this.size/2, this.y + this.size/2);
  ctx.rotate(this.rotation);
  const s = 0.9 + 0.13*Math.sin(this.sparkPhase + Date.now()*0.003);
  ctx.scale(s, s);
  ctx.drawImage(img, -this.size/2, -this.size/2, this.size, this.size);
  ctx.restore();
}

const pool = {
  arr: [],
  init(n){ this.arr.length = 0; for(let i=0;i<n;i++){ const s = new Star(); s.active = false; this.arr.push(s); } },
  get(){ for(let s of this.arr) if(!s.active) return s; const s = new Star(); s.active = false; this.arr.push(s); return s; }
};

function Cloud(x,y,scale,speed){ this.x=x; this.y=y; this.scale=scale; this.speed=speed; }
Cloud.prototype.update = function(dt){ this.x += this.speed * dt; if(this.x > GAME.width + 240) this.x = -280 - Math.random()*80; }
Cloud.prototype.draw = function(ctx,img){ ctx.save(); ctx.globalAlpha = 0.9; ctx.drawImage(img, this.x, this.y, img.naturalWidth*this.scale, img.naturalHeight*this.scale); ctx.restore(); }

let score = 0, lives = 3, lastSpawn = 0, running = true, gameOver=false;
const stars = [];
const clouds = [];
let last = performance.now()/1000;

function initGame(){
  score = 0; lives = 3; lastSpawn = 0; running = true; gameOver = false; document.getElementById('score').textContent = score; document.getElementById('lives').textContent = lives; document.getElementById('gameOverMsg').style.display='none';
  bucket.width = Math.min(300, GAME.width*0.34);
  bucket.height = bucket.width * 0.55;
  bucket.x = GAME.width/2 - bucket.width/2;
  bucket.y = GAME.height - bucket.height - 12;
  bucket.targetX = bucket.x;
  pool.init(GAME.maxStars);
  stars.length = 0;
  clouds.length = 0;
  clouds.push(new Cloud(-60, 40, 1.0, 10));
  clouds.push(new Cloud(40, 20, 0.8, 18));
  clouds.push(new Cloud(200, 70, 1.1, 8));
  last = performance.now()/1000;
}

const keys = {left:false,right:false};
window.addEventListener('keydown', (e)=>{
  if(e.key === 'ArrowLeft' || e.key === 'a' || e.key === 'A') keys.left = true;
  if(e.key === 'ArrowRight' || e.key === 'd' || e.key === 'D') keys.right = true;
});
window.addEventListener('keyup', (e)=>{
  if(e.key === 'ArrowLeft' || e.key === 'a' || e.key === 'A') keys.left = false;
  if(e.key === 'ArrowRight' || e.key === 'd' || e.key === 'D') keys.right = false;
});

const leftTouch = document.getElementById('leftTouch');
const rightTouch = document.getElementById('rightTouch');
let touchingLeft=false, touchingRight=false;
leftTouch.addEventListener('touchstart',(e)=>{ e.preventDefault(); touchingLeft = true; });
leftTouch.addEventListener('touchend',(e)=>{ e.preventDefault(); touchingLeft = false; });
leftTouch.addEventListener('touchcancel',(e)=>{ e.preventDefault(); touchingLeft = false; });
rightTouch.addEventListener('touchstart',(e)=>{ e.preventDefault(); touchingRight = true; });
rightTouch.addEventListener('touchend',(e)=>{ e.preventDefault(); touchingRight = false; });
rightTouch.addEventListener('touchcancel',(e)=>{ e.preventDefault(); touchingRight = false; });

let pointerDown=false, pointerOffsetX=0;
canvas.addEventListener('pointerdown',(e)=>{ pointerDown=true; const r=canvas.getBoundingClientRect(); const x=(e.clientX - r.left); pointerOffsetX = x - bucket.x; });
window.addEventListener('pointermove',(e)=>{ if(pointerDown){ const r=canvas.getBoundingClientRect(); const x=(e.clientX - r.left); bucket.targetX = x - pointerOffsetX; } });
window.addEventListener('pointerup',()=>{ pointerDown=false; });

document.getElementById('restartBtn').addEventListener('click', ()=>{ initGame(); if(audioCtx && audioCtx.state==='suspended') audioCtx.resume(); if(!running){ running = true; requestAnimationFrame(frame); } else { requestAnimationFrame(frame); } });

function spawnStar(){ const s = pool.get(); s.active = true; s.reset(); stars.push(s); }

function checkCatch(star){
  const bx = bucket.x, by = bucket.y, bw = bucket.width, bh = bucket.height;
  const sx = star.x, sy = star.y, ss = star.size;
  if(sx + ss > bx + 10 && sx < bx + bw - 10 && sy + ss > by + bh*0.18 && sy + ss < by + bh + 40) return true;
  return false;
}

function frame(nowms){
  const now = nowms/1000;
  let dt = now - last; if(dt > 0.05) dt = 0.05;
  last = now;

  lastSpawn += dt;
  const currentSpawnInterval = Math.max(0.35, GAME.spawnInterval - Math.min(0.5, score*0.02));
  if(lastSpawn > currentSpawnInterval && stars.length < GAME.maxStars){ lastSpawn = 0; spawnStar(); }

  if(keys.left || touchingLeft) bucket.targetX -= bucket.speed * dt * 0.9;
  if(keys.right || touchingRight) bucket.targetX += bucket.speed * dt * 0.9;
  bucket.targetX = clamp(bucket.targetX, 6, GAME.width - bucket.width - 6);
  bucket.x = lerp(bucket.x, bucket.targetX, clamp(1 - Math.pow(0.0001, dt*60), 0, 1));

  for(let c of clouds) c.update(dt);

  for(let i = stars.length - 1; i >= 0; i--){
    const s = stars[i]; s.update(dt);
    if(!s.collected && checkCatch(s)){
      s.collected = true; s.active = false; stars.splice(i,1);
      score++; document.getElementById('score').textContent = score;
      if(audioCtx && !isMuted) playCatchSound();
      continue;
    }
    if(s.y > GAME.height + 40){
      stars.splice(i,1); lives--; document.getElementById('lives').textContent = lives;
      if(lives <= 0){ gameOver = true; running=false; document.getElementById('gameOverMsg').style.display='block'; }
    }
  }

  draw();

  if(running) requestAnimationFrame(frame);
}

function draw(){
  ctx.clearRect(0,0,GAME.width, GAME.height);

  const g = ctx.createLinearGradient(0,0,0,GAME.height);
  g.addColorStop(0,'#9be2ff'); g.addColorStop(0.45,'#79d0ff'); g.addColorStop(1,'#dff6ff');
  ctx.fillStyle = g; ctx.fillRect(0,0,GAME.width,GAME.height);

  ctx.save();
  ctx.globalAlpha = 0.12;
  for(let i=0;i<30;i++){
    const x = (i*47 + (Date.now()*0.03)) % GAME.width;
    ctx.beginPath(); ctx.arc(x, 30 + (i%5)*14, 1.2 + ((i%3)/2), 0, Math.PI*2); ctx.fillStyle = '#fff'; ctx.fill();
  }
  ctx.restore();

  for(let i=0;i<clouds.length;i++){
    const c = clouds[i]; ctx.globalAlpha = i===0?0.9:0.95; c.draw(ctx, ASSETS.cloud);
  }

  for(let s of stars) s.draw(ctx, ASSETS.star);

  bucket.draw(ctx, ASSETS.bucket);

  ctx.save();
  const lgrad = ctx.createLinearGradient(0,GAME.height*0.6,0,GAME.height);
  lgrad.addColorStop(0,'rgba(255,255,255,0)'); lgrad.addColorStop(1,'rgba(0,0,0,0.08)');
  ctx.fillStyle = lgrad; ctx.fillRect(0,GAME.height*0.6,GAME.width,GAME.height*0.4);
  ctx.restore();
}

(async ()=>{
  await loadAssets();
  updateGameDims();
  initGame();
  const startAudioOnGesture = ()=>{ document.removeEventListener('pointerdown', startAudioOnGesture); if(!audioCtx) initAudio(); else audioCtx.resume(); };
  document.addEventListener('pointerdown', startAudioOnGesture);
  requestAnimationFrame(frame);
})();

window.__CTS = { spawnStar, pool };

</script>
</body>
</html>
