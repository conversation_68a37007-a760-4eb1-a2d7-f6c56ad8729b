<canvas id="gameCanvas"></canvas>

<script>
let canvas = document.getElementById('gameCanvas');
let ctx = canvas.getContext('2d');

function resizeCanvas() {
    canvas.width = window.innerWidth * 0.8;
    canvas.height = window.innerHeight * 0.8;
}
window.addEventListener('resize', resizeCanvas);

const SKY_THEMES = {
    classic: '#87CEEB',
    sunset: '#FFD580',
    night: '#0B3D91'
};

const BUCKETS = {
    red: 'red',
    blue: 'blue',
    green: 'green'
};

const STARS = {
    yellow: 'yellow',
    white: 'white',
    pink: 'pink'
};

let gameSettings = {
    currentSky: 'classic',
    currentBucket: 'red',
    currentStar: 'yellow'
};

function saveSettings() {
    localStorage.setItem('catchStarSettings', JSON.stringify(gameSettings));
}

function loadSettings() {
    try {
        const saved = localStorage.getItem('catchStarSettings');
        if (saved) gameSettings = { ...gameSettings, ...JSON.parse(saved) };
    } catch (e) {
        console.warn("Settings load failed:", e);
    }
}

function initCustomizationOptions() {
    Object.keys(SKY_THEMES).forEach(key => {
        let option = document.createElement('div');
        option.style.background = SKY_THEMES[key];
        option.style.width = '50px';
        option.style.height = '50px';
        option.style.display = 'inline-block';
        option.style.margin = '5px';
        option.style.cursor = 'pointer';
        option.onclick = (e) => selectSky(key, e);
        document.body.appendChild(option);
    });
}

function selectSky(key, e) {
    gameSettings.currentSky = key;
    saveSettings();
    e.currentTarget.style.border = '3px solid black';
}

let game = {
    score: 0,
    lives: 3,
    isRunning: false,
    isPaused: false,
    level: 1,
    lastTime: 0,
    deltaTime: 0
};

let bucket = {
    x: 200,
    y: 300,
    width: 80,
    height: 20,
    speed: 300
};

let stars = [];
let starSpawnTimer = 0;
let STAR_SPAWN_RATE = 0.8;
let STAR_SPEED = 150;

function spawnStar() {
    stars.push({
        x: Math.random() * (canvas.width - 20),
        y: -20,
        size: 20,
        color: gameSettings.currentStar,
        speed: STAR_SPEED
    });
}

function updateDifficulty() {
    STAR_SPAWN_RATE = Math.max(0.3, 0.8 - (game.level * 0.05));
    STAR_SPEED = 150 + (game.level * 10);
}

function startGame() {
    game.score = 0;
    game.lives = 3;
    game.level = 1;
    stars = [];
    updateDifficulty();
    game.isRunning = true;
    requestAnimationFrame(gameLoop);
}

function updateGame() {
    starSpawnTimer += game.deltaTime;
    if (starSpawnTimer >= STAR_SPAWN_RATE) {
        spawnStar();
        starSpawnTimer = 0;
    }

    stars.forEach(star => {
        star.y += star.speed * game.deltaTime;
    });

    stars = stars.filter(star => {
        if (star.y + star.size >= bucket.y &&
            star.x + star.size >= bucket.x &&
            star.x <= bucket.x + bucket.width) {
            game.score++;
            return false;
        }
        if (star.y > canvas.height) {
            game.lives--;
            return false;
        }
        return true;
    });

    if (game.lives <= 0) {
        game.isRunning = false;
        alert('Game Over! Score: ' + game.score);
    }
}

function drawGame() {
    ctx.fillStyle = SKY_THEMES[gameSettings.currentSky];
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    ctx.fillStyle = gameSettings.currentBucket;
    ctx.fillRect(bucket.x, bucket.y, bucket.width, bucket.height);

    stars.forEach(star => {
        ctx.fillStyle = star.color;
        ctx.beginPath();
        ctx.arc(star.x, star.y, star.size / 2, 0, Math.PI * 2);
        ctx.fill();
    });

    ctx.fillStyle = 'black';
    ctx.fillText('Score: ' + game.score, 10, 20);
    ctx.fillText('Lives: ' + game.lives, 10, 40);
}

function gameLoop(timestamp) {
    if (!game.isRunning) return;
    if (!game.lastTime) game.lastTime = timestamp;
    game.deltaTime = (timestamp - game.lastTime) / 1000;
    game.lastTime = timestamp;

    updateGame();
    drawGame();
    requestAnimationFrame(gameLoop);
}

window.onload = () => {
    resizeCanvas();
    loadSettings();
    initCustomizationOptions();
    startGame();
};
</script>
