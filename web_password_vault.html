<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 Web Password Vault</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
            color: #cdd6f4;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .login-card {
            background: rgba(22, 33, 62, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(49, 50, 68, 0.5);
            border-radius: 20px;
            padding: 40px;
            max-width: 450px;
            margin: 100px auto;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .app-icon {
            font-size: 48px;
            text-align: center;
            margin-bottom: 20px;
        }

        .app-title {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 10px;
            color: #cdd6f4;
        }

        .app-subtitle {
            text-align: center;
            color: #a6adc8;
            margin-bottom: 30px;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #cdd6f4;
        }

        .form-input {
            width: 100%;
            padding: 15px;
            border: 1px solid rgba(49, 50, 68, 0.5);
            border-radius: 12px;
            background: rgba(15, 52, 96, 0.8);
            color: #cdd6f4;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #e94560;
            box-shadow: 0 0 0 3px rgba(233, 69, 96, 0.1);
        }

        .btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 10px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #e94560 0%, #f38ba8 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(233, 69, 96, 0.3);
        }

        .btn-secondary {
            background: rgba(26, 26, 46, 0.8);
            color: #cdd6f4;
            border: 1px solid rgba(49, 50, 68, 0.5);
        }

        .btn-secondary:hover {
            background: rgba(49, 50, 68, 0.8);
        }

        .main-interface {
            display: none;
            animation: fadeIn 0.6s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .header {
            background: rgba(22, 33, 62, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .header-left h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .header-left p {
            color: #a6adc8;
            font-size: 14px;
        }

        .header-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn-small {
            padding: 10px 20px;
            border: none;
            border-radius: 10px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-add {
            background: #e94560;
            color: white;
        }

        .btn-generate {
            background: #00d4aa;
            color: white;
        }

        .btn-logout {
            background: #fb5607;
            color: white;
        }

        .btn-small:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .search-bar {
            background: rgba(22, 33, 62, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .search-input {
            width: 100%;
            padding: 12px 20px;
            border: 1px solid rgba(49, 50, 68, 0.5);
            border-radius: 10px;
            background: rgba(15, 52, 96, 0.8);
            color: #cdd6f4;
            font-size: 16px;
        }

        .passwords-grid {
            display: grid;
            gap: 20px;
        }

        .password-card {
            background: rgba(22, 33, 62, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(49, 50, 68, 0.5);
            border-radius: 15px;
            padding: 25px;
            transition: all 0.3s ease;
            animation: slideUp 0.4s ease-out;
        }

        .password-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
            border-color: #e94560;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .card-info h3 {
            font-size: 18px;
            margin-bottom: 5px;
            color: #cdd6f4;
        }

        .card-info p {
            color: #a6adc8;
            font-size: 14px;
        }

        .card-actions {
            display: flex;
            gap: 8px;
        }

        .btn-icon {
            width: 35px;
            height: 35px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn-view { background: #6c7086; }
        .btn-copy { background: #00d4aa; }
        .btn-edit { background: #e94560; }
        .btn-delete { background: #fb5607; }

        .btn-icon:hover {
            transform: scale(1.1);
        }

        .strength-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: bold;
            margin-top: 5px;
        }

        .strength-weak { background: #fb5607; color: white; }
        .strength-medium { background: #ffbe0b; color: black; }
        .strength-strong { background: #00d4aa; color: white; }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
            z-index: 1000;
            animation: fadeIn 0.3s ease-out;
        }

        .modal-content {
            background: rgba(22, 33, 62, 0.95);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(49, 50, 68, 0.5);
            border-radius: 20px;
            padding: 30px;
            max-width: 500px;
            margin: 50px auto;
            position: relative;
            animation: slideUp 0.4s ease-out;
        }

        .modal-header {
            text-align: center;
            margin-bottom: 25px;
        }

        .modal-header h2 {
            font-size: 20px;
            color: #cdd6f4;
        }

        .close-btn {
            position: absolute;
            top: 15px;
            right: 20px;
            background: none;
            border: none;
            font-size: 24px;
            color: #a6adc8;
            cursor: pointer;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c7086;
        }

        .empty-state .icon {
            font-size: 64px;
            margin-bottom: 20px;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 10px;
            color: white;
            font-weight: bold;
            z-index: 2000;
            animation: slideInRight 0.4s ease-out;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .notification.success { background: #00d4aa; }
        .notification.error { background: #fb5607; }
        .notification.warning { background: #ffbe0b; color: black; }

        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .header-buttons {
                justify-content: center;
            }

            .modal-content {
                margin: 20px;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Login Screen -->
        <div id="loginScreen" class="login-card">
            <div class="app-icon">🔐</div>
            <h1 class="app-title">Web Password Vault</h1>
            <p class="app-subtitle">Secure • Modern • Simple</p>
            
            <div class="form-group">
                <label class="form-label">Master Password</label>
                <input type="password" id="masterPassword" class="form-input" 
                       placeholder="Enter your master password">
            </div>
            
            <button class="btn btn-primary" onclick="login()">🔓 Unlock Vault</button>
            <button class="btn btn-secondary" onclick="createVault()">✨ Create New Vault</button>
            
            <p style="text-align: center; margin-top: 20px; font-size: 12px; color: #6c7086;">
                Your passwords are encrypted and stored locally in your browser
            </p>
        </div>

        <!-- Main Interface -->
        <div id="mainInterface" class="main-interface">
            <div class="header">
                <div class="header-left">
                    <h1>🔐 Password Vault</h1>
                    <p id="passwordCount">0 passwords stored</p>
                </div>
                <div class="header-buttons">
                    <button class="btn-small btn-add" onclick="showAddModal()">➕ Add</button>
                    <button class="btn-small btn-generate" onclick="showGenerateModal()">🎲 Generate</button>
                    <button class="btn-small btn-logout" onclick="logout()">🚪 Logout</button>
                </div>
            </div>

            <div class="search-bar">
                <input type="text" id="searchInput" class="search-input" 
                       placeholder="🔍 Search passwords..." oninput="filterPasswords()">
            </div>

            <div id="passwordsContainer" class="passwords-grid">
                <!-- Password cards will be inserted here -->
            </div>

            <div id="emptyState" class="empty-state" style="display: none;">
                <div class="icon">🔍</div>
                <h3>No passwords found</h3>
                <p>Add your first password to get started</p>
            </div>
        </div>
    </div>

    <!-- Add/Edit Password Modal -->
    <div id="passwordModal" class="modal">
        <div class="modal-content">
            <button class="close-btn" onclick="closeModal('passwordModal')">&times;</button>
            <div class="modal-header">
                <h2 id="modalTitle">Add New Password</h2>
            </div>

            <div class="form-group">
                <label class="form-label">Site/Service Name</label>
                <input type="text" id="siteName" class="form-input" placeholder="e.g., Google, Facebook">
            </div>

            <div class="form-group">
                <label class="form-label">Username/Email</label>
                <input type="text" id="username" class="form-input" placeholder="<EMAIL>">
            </div>

            <div class="form-group">
                <label class="form-label">Password</label>
                <div style="display: flex; gap: 10px;">
                    <input type="password" id="password" class="form-input" style="flex: 1;">
                    <button class="btn-small btn-generate" onclick="generatePasswordInModal()" style="width: auto; padding: 12px;">🎲</button>
                </div>
            </div>

            <div class="form-group">
                <label class="form-label">Website URL (Optional)</label>
                <input type="url" id="websiteUrl" class="form-input" placeholder="https://example.com">
            </div>

            <div style="display: flex; gap: 10px; margin-top: 25px;">
                <button class="btn btn-primary" onclick="savePassword()" style="flex: 1;">💾 Save Password</button>
                <button class="btn btn-secondary" onclick="closeModal('passwordModal')" style="flex: 1;">❌ Cancel</button>
            </div>
        </div>
    </div>

    <!-- View Password Modal -->
    <div id="viewModal" class="modal">
        <div class="modal-content">
            <button class="close-btn" onclick="closeModal('viewModal')">&times;</button>
            <div class="modal-header">
                <div style="font-size: 32px; margin-bottom: 10px;" id="viewIcon">🔐</div>
                <h2 id="viewSiteName">Site Name</h2>
            </div>

            <div class="form-group">
                <label class="form-label">Username</label>
                <div class="form-input" id="viewUsername" style="background: rgba(15, 52, 96, 0.5);">N/A</div>
            </div>

            <div class="form-group">
                <label class="form-label">Password</label>
                <div style="display: flex; gap: 10px; align-items: center;">
                    <div class="form-input" id="viewPassword" style="flex: 1; background: rgba(15, 52, 96, 0.5);">••••••••••••</div>
                    <button class="btn-icon btn-view" onclick="togglePasswordVisibility()" id="toggleBtn">👁️</button>
                </div>
            </div>

            <div class="form-group" id="viewUrlGroup" style="display: none;">
                <label class="form-label">Website URL</label>
                <div class="form-input" id="viewUrl" style="background: rgba(15, 52, 96, 0.5);"></div>
            </div>

            <div style="display: flex; gap: 10px; margin-top: 25px;">
                <button class="btn btn-primary" onclick="copyPasswordToClipboard()" style="flex: 1;">📋 Copy Password</button>
                <button class="btn btn-secondary" onclick="closeModal('viewModal')" style="flex: 1;">❌ Close</button>
            </div>
        </div>
    </div>

    <!-- Generate Password Modal -->
    <div id="generateModal" class="modal">
        <div class="modal-content">
            <button class="close-btn" onclick="closeModal('generateModal')">&times;</button>
            <div class="modal-header">
                <h2>🎲 Password Generator</h2>
            </div>

            <div class="form-group">
                <label class="form-label">Password Length: <span id="lengthValue">16</span></label>
                <input type="range" id="lengthSlider" min="8" max="32" value="16"
                       oninput="updateLengthValue()" style="width: 100%;">
            </div>

            <div class="form-group">
                <label><input type="checkbox" id="includeUpper" checked> Include Uppercase (A-Z)</label><br>
                <label><input type="checkbox" id="includeLower" checked> Include Lowercase (a-z)</label><br>
                <label><input type="checkbox" id="includeNumbers" checked> Include Numbers (0-9)</label><br>
                <label><input type="checkbox" id="includeSymbols" checked> Include Symbols (!@#$%)</label>
            </div>

            <div class="form-group">
                <label class="form-label">Generated Password</label>
                <textarea id="generatedPassword" class="form-input" rows="3" readonly
                         style="font-family: 'Courier New', monospace; font-weight: bold;"></textarea>
            </div>

            <div style="display: flex; gap: 10px; margin-top: 25px;">
                <button class="btn btn-primary" onclick="generateNewPassword()" style="flex: 1;">🎲 Generate</button>
                <button class="btn btn-secondary" onclick="copyGeneratedPassword()" style="flex: 1;">📋 Copy</button>
                <button class="btn btn-secondary" onclick="closeModal('generateModal')" style="flex: 1;">❌ Close</button>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let passwords = {};
        let masterPasswordHash = '';
        let currentEditingSite = null;
        let currentViewingSite = null;
        let passwordVisible = false;

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            loadData();

            // Enter key handlers
            document.getElementById('masterPassword').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') login();
            });
        });

        // Utility functions
        function hashPassword(password) {
            // Simple hash function for demo purposes
            let hash = 0;
            for (let i = 0; i < password.length; i++) {
                const char = password.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // Convert to 32-bit integer
            }
            return hash.toString();
        }

        function generateSecurePassword(length = 16, options = {}) {
            const uppercase = options.uppercase !== false ? 'ABCDEFGHIJKLMNOPQRSTUVWXYZ' : '';
            const lowercase = options.lowercase !== false ? 'abcdefghijklmnopqrstuvwxyz' : '';
            const numbers = options.numbers !== false ? '0123456789' : '';
            const symbols = options.symbols !== false ? '!@#$%^&*()_+-=[]{}|;:,.<>?' : '';

            const chars = uppercase + lowercase + numbers + symbols;
            if (!chars) return '';

            let password = '';
            for (let i = 0; i < length; i++) {
                password += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return password;
        }

        function getPasswordStrength(password) {
            if (password.length < 6) return 'weak';

            let score = 0;
            if (password.length >= 8) score++;
            if (/[A-Z]/.test(password)) score++;
            if (/[a-z]/.test(password)) score++;
            if (/[0-9]/.test(password)) score++;
            if (/[^A-Za-z0-9]/.test(password)) score++;

            return score >= 4 ? 'strong' : score >= 2 ? 'medium' : 'weak';
        }

        function getSiteIcon(site) {
            const siteIcons = {
                'google': '🌐', 'gmail': '📧', 'facebook': '📘', 'twitter': '🐦',
                'instagram': '📷', 'linkedin': '💼', 'github': '🐙', 'amazon': '📦',
                'netflix': '🎬', 'spotify': '🎵', 'youtube': '📺', 'discord': '🎮',
                'reddit': '🤖', 'pinterest': '📌', 'dropbox': '📁', 'microsoft': '🪟',
                'apple': '🍎', 'steam': '🎮', 'paypal': '💳', 'bank': '🏦'
            };

            const siteLower = site.toLowerCase();
            for (const [key, icon] of Object.entries(siteIcons)) {
                if (siteLower.includes(key)) return icon;
            }
            return '🔐';
        }

        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Data management
        function saveData() {
            const data = {
                masterHash: masterPasswordHash,
                passwords: passwords
            };
            localStorage.setItem('webPasswordVault', JSON.stringify(data));
        }

        function loadData() {
            const data = localStorage.getItem('webPasswordVault');
            if (data) {
                const parsed = JSON.parse(data);
                masterPasswordHash = parsed.masterHash || '';
                passwords = parsed.passwords || {};
            }
        }

        // Authentication
        function login() {
            const password = document.getElementById('masterPassword').value;
            if (!password) {
                showNotification('Please enter your master password!', 'warning');
                return;
            }

            if (masterPasswordHash && hashPassword(password) === masterPasswordHash) {
                showMainInterface();
                showNotification('Vault unlocked successfully!');
            } else if (!masterPasswordHash) {
                showNotification('No vault found. Create a new one!', 'warning');
            } else {
                showNotification('Invalid master password!', 'error');
            }
        }

        function createVault() {
            const password = document.getElementById('masterPassword').value;
            if (!password) {
                showNotification('Please enter a master password!', 'warning');
                return;
            }

            if (password.length < 6) {
                showNotification('Master password must be at least 6 characters!', 'warning');
                return;
            }

            masterPasswordHash = hashPassword(password);
            passwords = {};
            saveData();
            showMainInterface();
            showNotification('Vault created successfully!');
        }

        function logout() {
            passwords = {};
            masterPasswordHash = '';
            currentEditingSite = null;
            currentViewingSite = null;
            document.getElementById('loginScreen').style.display = 'block';
            document.getElementById('mainInterface').style.display = 'none';
            document.getElementById('masterPassword').value = '';
            showNotification('Logged out successfully!');
        }

        // Interface management
        function showMainInterface() {
            document.getElementById('loginScreen').style.display = 'none';
            document.getElementById('mainInterface').style.display = 'block';
            updatePasswordCount();
            renderPasswords();
        }

        function updatePasswordCount() {
            const count = Object.keys(passwords).length;
            document.getElementById('passwordCount').textContent = `${count} password${count !== 1 ? 's' : ''} stored`;
        }

        function renderPasswords() {
            const container = document.getElementById('passwordsContainer');
            const emptyState = document.getElementById('emptyState');
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();

            // Filter passwords
            const filteredPasswords = Object.entries(passwords).filter(([site, data]) => {
                return site.toLowerCase().includes(searchTerm) ||
                       (data.username && data.username.toLowerCase().includes(searchTerm));
            });

            if (filteredPasswords.length === 0) {
                container.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            container.style.display = 'grid';
            emptyState.style.display = 'none';

            container.innerHTML = filteredPasswords.map(([site, data]) => {
                const strength = getPasswordStrength(data.password);
                const icon = getSiteIcon(site);

                return `
                    <div class="password-card">
                        <div class="card-header">
                            <div class="card-info">
                                <h3>${icon} ${site}</h3>
                                <p>👤 ${data.username || 'N/A'}</p>
                                <span class="strength-indicator strength-${strength}">🔒 ${strength.toUpperCase()}</span>
                            </div>
                            <div class="card-actions">
                                <button class="btn-icon btn-view" onclick="viewPassword('${site}')" title="View">👁️</button>
                                <button class="btn-icon btn-copy" onclick="copyPassword('${site}')" title="Copy">📋</button>
                                <button class="btn-icon btn-edit" onclick="editPassword('${site}')" title="Edit">✏️</button>
                                <button class="btn-icon btn-delete" onclick="deletePassword('${site}')" title="Delete">🗑️</button>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function filterPasswords() {
            renderPasswords();
        }

        // Modal management
        function showModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            if (modalId === 'passwordModal') {
                clearPasswordForm();
            }
        }

        function clearPasswordForm() {
            document.getElementById('siteName').value = '';
            document.getElementById('username').value = '';
            document.getElementById('password').value = '';
            document.getElementById('websiteUrl').value = '';
            currentEditingSite = null;
        }

        // Password management
        function showAddModal() {
            document.getElementById('modalTitle').textContent = 'Add New Password';
            clearPasswordForm();
            showModal('passwordModal');
        }

        function savePassword() {
            const site = document.getElementById('siteName').value.trim();
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const url = document.getElementById('websiteUrl').value.trim();

            if (!site) {
                showNotification('Please enter a site name!', 'warning');
                return;
            }

            if (!password) {
                showNotification('Please enter a password!', 'warning');
                return;
            }

            passwords[site] = {
                username: username,
                password: password,
                url: url
            };

            saveData();
            renderPasswords();
            updatePasswordCount();
            closeModal('passwordModal');
            showNotification(`Password for ${site} saved!`);
        }

        function viewPassword(site) {
            if (!passwords[site]) return;

            const data = passwords[site];
            currentViewingSite = site;
            passwordVisible = false;

            document.getElementById('viewSiteName').textContent = site;
            document.getElementById('viewIcon').textContent = getSiteIcon(site);
            document.getElementById('viewUsername').textContent = data.username || 'N/A';
            document.getElementById('viewPassword').textContent = '••••••••••••';
            document.getElementById('toggleBtn').textContent = '👁️';

            if (data.url) {
                document.getElementById('viewUrl').textContent = data.url;
                document.getElementById('viewUrlGroup').style.display = 'block';
            } else {
                document.getElementById('viewUrlGroup').style.display = 'none';
            }

            showModal('viewModal');
        }

        function togglePasswordVisibility() {
            if (!currentViewingSite || !passwords[currentViewingSite]) return;

            const passwordElement = document.getElementById('viewPassword');
            const toggleBtn = document.getElementById('toggleBtn');

            if (passwordVisible) {
                passwordElement.textContent = '••••••••••••';
                toggleBtn.textContent = '👁️';
                passwordVisible = false;
            } else {
                passwordElement.textContent = passwords[currentViewingSite].password;
                toggleBtn.textContent = '🙈';
                passwordVisible = true;
            }
        }

        function copyPassword(site) {
            if (!passwords[site]) return;

            navigator.clipboard.writeText(passwords[site].password).then(() => {
                showNotification(`Password for ${site} copied!`);
            }).catch(() => {
                showNotification('Failed to copy password', 'error');
            });
        }

        function copyPasswordToClipboard() {
            if (!currentViewingSite || !passwords[currentViewingSite]) return;

            navigator.clipboard.writeText(passwords[currentViewingSite].password).then(() => {
                showNotification(`Password for ${currentViewingSite} copied!`);
            }).catch(() => {
                showNotification('Failed to copy password', 'error');
            });
        }

        function editPassword(site) {
            if (!passwords[site]) return;

            const data = passwords[site];
            currentEditingSite = site;

            document.getElementById('modalTitle').textContent = `Edit Password - ${site}`;
            document.getElementById('siteName').value = site;
            document.getElementById('username').value = data.username || '';
            document.getElementById('password').value = data.password;
            document.getElementById('websiteUrl').value = data.url || '';

            showModal('passwordModal');
        }

        function deletePassword(site) {
            if (!passwords[site]) return;

            if (confirm(`Are you sure you want to delete the password for ${site}?\n\nThis action cannot be undone.`)) {
                delete passwords[site];
                saveData();
                renderPasswords();
                updatePasswordCount();
                showNotification(`Password for ${site} deleted!`);
            }
        }

        // Password generation
        function showGenerateModal() {
            generateNewPassword();
            showModal('generateModal');
        }

        function updateLengthValue() {
            const length = document.getElementById('lengthSlider').value;
            document.getElementById('lengthValue').textContent = length;
            generateNewPassword();
        }

        function generateNewPassword() {
            const length = parseInt(document.getElementById('lengthSlider').value);
            const options = {
                uppercase: document.getElementById('includeUpper').checked,
                lowercase: document.getElementById('includeLower').checked,
                numbers: document.getElementById('includeNumbers').checked,
                symbols: document.getElementById('includeSymbols').checked
            };

            if (!options.uppercase && !options.lowercase && !options.numbers && !options.symbols) {
                showNotification('Please select at least one character type!', 'warning');
                return;
            }

            const password = generateSecurePassword(length, options);
            document.getElementById('generatedPassword').value = password;
        }

        function generatePasswordInModal() {
            const password = generateSecurePassword(16);
            document.getElementById('password').value = password;
            showNotification('Secure password generated!');
        }

        function copyGeneratedPassword() {
            const password = document.getElementById('generatedPassword').value;
            if (!password) {
                showNotification('No password to copy!', 'warning');
                return;
            }

            navigator.clipboard.writeText(password).then(() => {
                showNotification('Password copied to clipboard!');
            }).catch(() => {
                showNotification('Failed to copy password', 'error');
            });
        }

        // Event listeners for checkboxes
        document.addEventListener('DOMContentLoaded', function() {
            const checkboxes = ['includeUpper', 'includeLower', 'includeNumbers', 'includeSymbols'];
            checkboxes.forEach(id => {
                document.getElementById(id).addEventListener('change', generateNewPassword);
            });
        });

        // Close modals when clicking outside
        window.addEventListener('click', function(event) {
            const modals = ['passwordModal', 'viewModal', 'generateModal'];
            modals.forEach(modalId => {
                const modal = document.getElementById(modalId);
                if (event.target === modal) {
                    closeModal(modalId);
                }
            });
        });
    </script>
</body>
</html>
