<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Catch the Star</title>
    <style>
        body {
            margin: 0;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background: linear-gradient(to bottom, #87CEEB, #ffffff); /* Simple gradient sky */
            font-family: 'Arial', sans-serif;
            color: #333;
        }
        canvas {
            border: 2px solid #333;
            box-shadow: 0 0 20px rgba(0,0,0,0.2);
            background-color: rgba(255, 255, 255, 0.7);
        }
        #game-ui {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 40px;
            font-size: 24px;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
        }
    </style>
</head>
<body>
    <div id="game-ui">
        <div id="score">Score: 0</div>
        <div id="lives">Lives: 3</div>
    </div>
    <canvas id="gameCanvas"></canvas>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        const scoreElement = document.getElementById('score');
        const livesElement = document.getElementById('lives');

        // Set canvas size
        canvas.width = 600;
        canvas.height = 800;

        // Game state
        let score = 0;
        let lives = 3;
        let gameOver = false;

        // Player (Bucket)
        const bucket = {
            width: 100,
            height: 20,
            x: canvas.width / 2 - 50,
            y: canvas.height - 40,
            speed: 8,
            isMovingLeft: false,
            isMovingRight: false
        };

        // Stars
        const stars = [];
        const starRadius = 15;
        let lastStarTime = 0;
        const starInterval = 800; // Time in milliseconds between stars
        const starSpeed = 3;

        // Game loop variables
        let lastTime = 0;
        const fps = 60;
        const frameTime = 1000 / fps;

        // Event Listeners for movement
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft' || e.key === 'a') {
                bucket.isMovingLeft = true;
            } else if (e.key === 'ArrowRight' || e.key === 'd') {
                bucket.isMovingRight = true;
            }
        });

        document.addEventListener('keyup', (e) => {
            if (e.key === 'ArrowLeft' || e.key === 'a') {
                bucket.isMovingLeft = false;
            } else if (e.key === 'ArrowRight' || e.key === 'd') {
                bucket.isMovingRight = false;
            }
        });

        // Touch controls for mobile
        canvas.addEventListener('touchstart', handleTouchStart);
        canvas.addEventListener('touchmove', handleTouchMove);
        canvas.addEventListener('touchend', handleTouchEnd);

        let touchStartX = null;
        function handleTouchStart(e) {
            e.preventDefault();
            touchStartX = e.touches[0].clientX;
        }

        function handleTouchMove(e) {
            e.preventDefault();
            const touchX = e.touches[0].clientX;
            const canvasRect = canvas.getBoundingClientRect();
            const touchXInCanvas = touchX - canvasRect.left;

            if (touchXInCanvas < bucket.x + bucket.width / 2) {
                bucket.isMovingLeft = true;
                bucket.isMovingRight = false;
            } else {
                bucket.isMovingRight = true;
                bucket.isMovingLeft = false;
            }
        }

        function handleTouchEnd(e) {
            e.preventDefault();
            bucket.isMovingLeft = false;
            bucket.isMovingRight = false;
        }

        // Draw functions
        function drawBucket() {
            ctx.fillStyle = '#6D6D6D';
            ctx.fillRect(bucket.x, bucket.y, bucket.width, bucket.height);
            // Simple shadow effect
            ctx.fillStyle = 'rgba(0,0,0,0.2)';
            ctx.fillRect(bucket.x + 5, bucket.y + 5, bucket.width, bucket.height);
        }

        function drawStars() {
            ctx.fillStyle = '#FFD700'; // Gold color for stars
            stars.forEach(star => {
                ctx.beginPath();
                ctx.arc(star.x, star.y, starRadius, 0, Math.PI * 2);
                ctx.fill();
            });
        }

        function update() {
            if (gameOver) return;

            // Update bucket position
            if (bucket.isMovingLeft && bucket.x > 0) {
                bucket.x -= bucket.speed;
            }
            if (bucket.isMovingRight && bucket.x < canvas.width - bucket.width) {
                bucket.x += bucket.speed;
            }

            // Update stars
            stars.forEach(star => star.y += starSpeed);

            // Spawn new stars
            if (Date.now() - lastStarTime > starInterval) {
                stars.push({
                    x: Math.random() * (canvas.width - starRadius * 2) + starRadius,
                    y: -starRadius
                });
                lastStarTime = Date.now();
            }

            // Collision detection and cleanup
            for (let i = stars.length - 1; i >= 0; i--) {
                const star = stars[i];

                // Check if star is caught by bucket
                if (star.y + starRadius > bucket.y &&
                    star.x > bucket.x &&
                    star.x < bucket.x + bucket.width) {
                    score++;
                    scoreElement.textContent = `Score: ${score}`;
                    stars.splice(i, 1);
                }
                // Check if star is missed
                else if (star.y > canvas.height) {
                    lives--;
                    livesElement.textContent = `Lives: ${lives}`;
                    stars.splice(i, 1);
                }
            }

            if (lives <= 0) {
                gameOver = true;
                alert(`Game Over! Your final score is: ${score}`);
            }
        }

        function gameLoop(timestamp) {
            const deltaTime = timestamp - lastTime;
            if (deltaTime > frameTime) {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                drawBucket();
                drawStars();
                
                update();
                
                lastTime = timestamp - (deltaTime % frameTime);
            }
            requestAnimationFrame(gameLoop);
        }

        requestAnimationFrame(gameLoop);
    </script>
</body>
</html>