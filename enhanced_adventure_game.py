#!/usr/bin/env python3
"""
Enhanced Text-Based Adventure Game
A comprehensive adventure game with character stats, combat system, and multiple endings.
"""

import time
import sys
import random
import json
from typing import Dict, List, Optional

class Player:
    """Player character with stats and inventory management."""
    
    def __init__(self, name: str = "Adventurer"):
        self.name = name
        self.health = 100
        self.max_health = 100
        self.attack = 10
        self.defense = 5
        self.gold = 0
        self.experience = 0
        self.level = 1
        self.inventory = []
        self.equipped_weapon = None
        self.equipped_armor = None
    
    def add_item(self, item: str):
        """Add item to inventory."""
        self.inventory.append(item)
        print(f"✅ Added {item} to inventory!")
    
    def remove_item(self, item: str) -> bool:
        """Remove item from inventory if it exists."""
        if item in self.inventory:
            self.inventory.remove(item)
            return True
        return False
    
    def has_item(self, item: str) -> bool:
        """Check if player has specific item."""
        return item in self.inventory
    
    def show_stats(self):
        """Display player statistics."""
        print(f"\n📊 {self.name}'s Stats:")
        print(f"❤️  Health: {self.health}/{self.max_health}")
        print(f"⚔️  Attack: {self.attack}")
        print(f"🛡️  Defense: {self.defense}")
        print(f"💰 Gold: {self.gold}")
        print(f"⭐ Level: {self.level} (XP: {self.experience})")
        print(f"🎒 Inventory: {', '.join(self.inventory) if self.inventory else 'Empty'}")
        if self.equipped_weapon:
            print(f"⚔️  Equipped: {self.equipped_weapon}")
        if self.equipped_armor:
            print(f"🛡️  Equipped: {self.equipped_armor}")
    
    def heal(self, amount: int):
        """Heal the player."""
        old_health = self.health
        self.health = min(self.max_health, self.health + amount)
        healed = self.health - old_health
        if healed > 0:
            print(f"💚 Healed for {healed} HP!")
        else:
            print("💚 Already at full health!")
    
    def take_damage(self, damage: int):
        """Apply damage to player."""
        actual_damage = max(1, damage - self.defense)
        self.health -= actual_damage
        print(f"💥 You take {actual_damage} damage! Health: {self.health}/{self.max_health}")
        return self.health <= 0
    
    def gain_experience(self, xp: int):
        """Add experience and check for level up."""
        self.experience += xp
        print(f"⭐ Gained {xp} XP!")
        
        # Level up every 100 XP
        while self.experience >= self.level * 100:
            self.level_up()
    
    def level_up(self):
        """Level up the player."""
        self.level += 1
        self.max_health += 20
        self.health = self.max_health  # Full heal on level up
        self.attack += 3
        self.defense += 2
        print(f"🎉 LEVEL UP! You are now level {self.level}!")
        print(f"💪 Stats increased! Health: {self.max_health}, Attack: {self.attack}, Defense: {self.defense}")

class Enemy:
    """Enemy class for combat encounters."""
    
    def __init__(self, name: str, health: int, attack: int, defense: int, xp_reward: int, gold_reward: int):
        self.name = name
        self.health = health
        self.max_health = health
        self.attack = attack
        self.defense = defense
        self.xp_reward = xp_reward
        self.gold_reward = gold_reward
    
    def take_damage(self, damage: int) -> bool:
        """Apply damage to enemy and return True if defeated."""
        actual_damage = max(1, damage - self.defense)
        self.health -= actual_damage
        print(f"💥 {self.name} takes {actual_damage} damage! Health: {self.health}/{self.max_health}")
        return self.health <= 0

# Game utilities
def slow_type(text: str, delay: float = 0.03):
    """Type text with delay for dramatic effect."""
    for char in text:
        sys.stdout.write(char)
        sys.stdout.flush()
        time.sleep(delay)
    print()

def get_choice(prompt: str, valid_choices: List[str]) -> str:
    """Get valid input from player."""
    while True:
        choice = input(f"\n{prompt} ").lower().strip()
        if choice in valid_choices:
            return choice
        print(f"❌ Invalid choice. Please choose from: {', '.join(valid_choices)}")

def combat(player: Player, enemy: Enemy) -> bool:
    """Combat system. Returns True if player wins."""
    print(f"\n⚔️  COMBAT: {player.name} vs {enemy.name}!")
    print(f"🐉 {enemy.name}: {enemy.health} HP, {enemy.attack} ATK, {enemy.defense} DEF")
    
    while player.health > 0 and enemy.health > 0:
        # Player turn
        action = get_choice("Choose action: (a)ttack, (d)efend, (i)nventory, (r)un", 
                          ["a", "attack", "d", "defend", "i", "inventory", "r", "run"])
        
        if action in ["a", "attack"]:
            damage = random.randint(player.attack - 2, player.attack + 2)
            if enemy.take_damage(damage):
                print(f"🎉 You defeated {enemy.name}!")
                player.gain_experience(enemy.xp_reward)
                player.gold += enemy.gold_reward
                print(f"💰 Found {enemy.gold_reward} gold!")
                return True
        
        elif action in ["d", "defend"]:
            print("🛡️  You brace for impact, reducing incoming damage!")
            defend_bonus = 3
        
        elif action in ["i", "inventory"]:
            player.show_stats()
            continue  # Don't advance turn
        
        elif action in ["r", "run"]:
            if random.random() < 0.7:  # 70% chance to escape
                print("💨 You successfully ran away!")
                return False
            else:
                print("❌ Couldn't escape!")
        
        # Enemy turn
        if enemy.health > 0:
            enemy_damage = random.randint(enemy.attack - 1, enemy.attack + 1)
            if action in ["d", "defend"]:
                enemy_damage = max(1, enemy_damage - defend_bonus)
            
            if player.take_damage(enemy_damage):
                print("💀 You have been defeated!")
                return False
    
    return player.health > 0

# Initialize global player
player = None

def game_intro():
    """Introduction and character creation."""
    global player
    player = Player()  # Initialize player here

    slow_type("🌟 Welcome to the Enhanced Adventure Game! 🌟")
    slow_type("You are about to embark on an epic journey...")

    name = input("\nWhat is your name, brave adventurer? ").strip()
    if name:
        player.name = name

    slow_type(f"\nWelcome, {player.name}! Your adventure begins now...")
    player.show_stats()

    slow_type("\n🏰 You find yourself standing before three mysterious paths...")
    slow_type("Each path leads to different adventures and challenges.")
    main_crossroads()

def main_crossroads():
    """Main hub with three initial paths."""
    slow_type(f"\n🗺️  You stand at the crossroads, {player.name}.")
    slow_type("Three paths stretch before you:")
    slow_type("1. 🌲 FOREST PATH - You hear birds chirping and leaves rustling")
    slow_type("2. 🏔️  MOUNTAIN PATH - A steep, rocky trail disappears into mist")
    slow_type("3. 🏜️  DESERT PATH - Hot sand and mysterious mirages await")
    
    choice = get_choice("Which path do you choose? (forest/mountain/desert)", 
                       ["forest", "mountain", "desert", "1", "2", "3"])
    
    if choice in ["forest", "1"]:
        forest_path()
    elif choice in ["mountain", "2"]:
        mountain_path()
    elif choice in ["desert", "3"]:
        desert_path()

def forest_path():
    """Forest adventure path."""
    slow_type(f"\n🌲 {player.name} ventures into the Enchanted Forest...")
    slow_type("Sunlight filters through ancient trees, and magical creatures dart between shadows.")
    
    # Random encounter
    if random.random() < 0.6:  # 60% chance of encounter
        encounters = [
            ("friendly_fairy", "✨ A glowing fairy approaches you with a warm smile."),
            ("wild_wolf", "🐺 A fierce wolf blocks your path, growling menacingly."),
            ("treasure_chest", "💎 You discover a mysterious treasure chest hidden behind a tree.")
        ]
        
        encounter_type, description = random.choice(encounters)
        slow_type(description)
        
        if encounter_type == "friendly_fairy":
            fairy_encounter()
        elif encounter_type == "wild_wolf":
            wolf_encounter()
        elif encounter_type == "treasure_chest":
            treasure_encounter()
    else:
        slow_type("🍄 You find a peaceful clearing with glowing mushrooms.")
        slow_type("The mushrooms seem to have healing properties.")
        if get_choice("Do you eat the mushrooms? (yes/no)", ["yes", "no"]) == "yes":
            if random.random() < 0.8:  # 80% chance of healing
                player.heal(25)
            else:
                slow_type("💚 The mushrooms taste terrible but you feel energized!")
                player.gain_experience(10)
    
    continue_adventure()

def fairy_encounter():
    """Encounter with a friendly fairy."""
    slow_type("✨ The fairy speaks in a melodic voice:")
    slow_type("'Greetings, traveler! I can offer you a gift.'")

    choice = get_choice("What do you ask for? (healing/weapon/wisdom)",
                       ["healing", "weapon", "wisdom"])

    if choice == "healing":
        player.heal(50)
        slow_type("✨ The fairy's magic restores your vitality!")
    elif choice == "weapon":
        player.add_item("Fairy Blade")
        player.attack += 5
        slow_type("✨ The fairy grants you a magical blade that glows with inner light!")
    elif choice == "wisdom":
        player.gain_experience(50)
        slow_type("✨ The fairy shares ancient knowledge with you!")

def wolf_encounter():
    """Combat encounter with a wolf."""
    wolf = Enemy("Forest Wolf", 40, 12, 2, 25, 15)
    if combat(player, wolf):
        slow_type("🐺 As the wolf falls, you notice it was protecting something...")
        if random.random() < 0.5:
            player.add_item("Wolf Pelt")
            slow_type("🧥 You carefully take the wolf's pelt - it might be useful for armor.")

def treasure_encounter():
    """Finding a treasure chest."""
    slow_type("💎 The chest is locked with an intricate mechanism.")

    if player.has_item("Lockpick"):
        slow_type("🔓 You use your lockpick to open the chest!")
        success = True
    else:
        choice = get_choice("How do you open it? (force/examine/leave)",
                           ["force", "examine", "leave"])

        if choice == "force":
            if random.random() < 0.6:  # 60% success rate
                slow_type("💪 You manage to break the lock!")
                success = True
            else:
                slow_type("💥 The chest explodes with a trap! You take damage.")
                player.take_damage(15)
                success = False
        elif choice == "examine":
            slow_type("🔍 You carefully examine the lock and find a hidden mechanism!")
            success = True
            player.gain_experience(20)  # Bonus XP for being clever
        else:
            slow_type("🚶 You decide to leave the chest alone.")
            success = False

    if success:
        treasure_items = ["Gold Coins", "Health Potion", "Magic Ring", "Ancient Map"]
        found_item = random.choice(treasure_items)
        player.add_item(found_item)

        if found_item == "Gold Coins":
            gold_found = random.randint(20, 50)
            player.gold += gold_found
            slow_type(f"💰 You found {gold_found} gold coins!")
        elif found_item == "Health Potion":
            slow_type("🧪 A healing potion - it will restore health when used!")
        elif found_item == "Magic Ring":
            player.defense += 3
            slow_type("💍 The magic ring increases your defense!")
        elif found_item == "Ancient Map":
            slow_type("🗺️  An ancient map revealing secret locations!")

def mountain_path():
    """Mountain adventure path."""
    slow_type(f"\n🏔️  {player.name} begins the treacherous climb up the mountain...")
    slow_type("The air grows thin and cold as you ascend the rocky path.")

    # Mountain encounters
    encounters = [
        ("dragon_cave", "🐉 You discover a cave with ominous smoke rising from within."),
        ("mountain_troll", "👹 A massive troll blocks the mountain pass, demanding a toll."),
        ("ancient_shrine", "⛩️  You find an ancient shrine carved into the mountainside.")
    ]

    encounter_type, description = random.choice(encounters)
    slow_type(description)

    if encounter_type == "dragon_cave":
        dragon_encounter()
    elif encounter_type == "mountain_troll":
        troll_encounter()
    elif encounter_type == "ancient_shrine":
        shrine_encounter()

    continue_adventure()

def dragon_encounter():
    """Epic dragon boss fight."""
    slow_type("🐉 You peer into the cave and see a MASSIVE dragon sleeping on a pile of gold!")
    slow_type("This is clearly a dangerous situation...")

    choice = get_choice("What do you do? (sneak/challenge/retreat)",
                       ["sneak", "challenge", "retreat"])

    if choice == "sneak":
        if player.has_item("Stealth Cloak") or random.random() < 0.3:  # 30% base chance
            slow_type("🤫 You successfully sneak past and grab some treasure!")
            player.gold += random.randint(50, 100)
            player.gain_experience(40)
        else:
            slow_type("👁️  The dragon's eye opens! You've been spotted!")
            dragon_fight()
    elif choice == "challenge":
        slow_type("⚔️  You boldly challenge the dragon to combat!")
        dragon_fight()
    else:
        slow_type("🏃 Discretion is the better part of valor. You retreat safely.")

def dragon_fight():
    """Fight the dragon boss."""
    dragon = Enemy("Ancient Dragon", 120, 25, 8, 100, 200)
    slow_type("🔥 The dragon awakens with a mighty roar!")

    if combat(player, dragon):
        slow_type("🎉 INCREDIBLE! You have slain the mighty dragon!")
        slow_type("🏆 You are now a legendary dragon slayer!")
        player.add_item("Dragon Scale Armor")
        player.defense += 10
        player.add_item("Dragon's Hoard Key")
        slow_type("🗝️  The dragon's hoard key will unlock great treasures!")

def troll_encounter():
    """Encounter with mountain troll."""
    slow_type("👹 The troll speaks in a deep, rumbling voice:")
    slow_type("'None shall pass without paying the toll!'")

    choice = get_choice("How do you respond? (pay/fight/riddle)",
                       ["pay", "fight", "riddle"])

    if choice == "pay":
        if player.gold >= 30:
            player.gold -= 30
            slow_type("💰 You pay the troll 30 gold. He steps aside grudgingly.")
            slow_type("👹 'Smart choice, little one.'")
        else:
            slow_type("💸 You don't have enough gold! The troll attacks!")
            troll_fight()
    elif choice == "fight":
        slow_type("⚔️  You draw your weapon and prepare for battle!")
        troll_fight()
    elif choice == "riddle":
        slow_type("🧩 You challenge the troll to a battle of wits!")
        riddle_challenge()

def troll_fight():
    """Fight the mountain troll."""
    troll = Enemy("Mountain Troll", 80, 18, 6, 60, 40)
    if combat(player, troll):
        slow_type("🎯 The troll falls with a thunderous crash!")
        player.add_item("Troll Club")
        slow_type("🏏 You take the troll's massive club as a trophy!")

def riddle_challenge():
    """Riddle mini-game with the troll."""
    riddles = [
        ("What has keys but no locks, space but no room, and you can enter but not go inside?", "keyboard"),
        ("I speak without a mouth and hear without ears. What am I?", "echo"),
        ("The more you take, the more you leave behind. What am I?", "footsteps")
    ]

    riddle, answer = random.choice(riddles)
    slow_type(f"👹 The troll grins and asks: '{riddle}'")

    player_answer = input("Your answer: ").lower().strip()

    if answer in player_answer:
        slow_type("🎉 Correct! The troll is impressed and lets you pass!")
        slow_type("👹 'You are wise, traveler. Take this gift.'")
        player.add_item("Troll's Wisdom Stone")
        player.gain_experience(30)
    else:
        slow_type(f"❌ Wrong! The answer was '{answer}'. The troll attacks!")
        troll_fight()

def shrine_encounter():
    """Ancient shrine with mystical properties."""
    slow_type("⛩️  The ancient shrine pulses with mystical energy.")
    slow_type("Strange runes glow softly on its surface.")

    choice = get_choice("What do you do? (pray/study/offer)",
                       ["pray", "study", "offer"])

    if choice == "pray":
        slow_type("🙏 You kneel and pray at the shrine...")
        blessing = random.choice(["health", "strength", "wisdom"])

        if blessing == "health":
            player.max_health += 20
            player.heal(20)
            slow_type("✨ The shrine blesses you with vitality!")
        elif blessing == "strength":
            player.attack += 5
            slow_type("✨ The shrine blesses you with strength!")
        else:
            player.gain_experience(50)
            slow_type("✨ The shrine blesses you with wisdom!")

    elif choice == "study":
        slow_type("📚 You carefully study the ancient runes...")
        player.add_item("Ancient Knowledge")
        player.gain_experience(40)
        slow_type("🧠 You gain understanding of the ancient language!")

    elif choice == "offer":
        if player.gold >= 20:
            player.gold -= 20
            slow_type("💰 You offer 20 gold to the shrine...")
            slow_type("✨ The shrine accepts your offering and grants you a powerful blessing!")
            player.attack += 3
            player.defense += 3
            player.max_health += 15
        else:
            slow_type("💸 You don't have enough gold to make a proper offering.")

def desert_path():
    """Desert adventure path."""
    slow_type(f"\n🏜️  {player.name} ventures into the vast desert...")
    slow_type("The sun beats down mercilessly as sand stretches endlessly in all directions.")

    # Desert survival challenge
    if not player.has_item("Water Flask"):
        slow_type("💧 The heat is overwhelming! You need water urgently.")
        if random.random() < 0.4:  # 40% chance of finding oasis
            slow_type("🌴 Miraculously, you spot an oasis in the distance!")
            oasis_encounter()
        else:
            slow_type("🔥 You suffer from dehydration!")
            player.take_damage(20)
            if player.health <= 0:
                slow_type("💀 You perish in the desert heat...")
                game_over()
                return

    # Desert encounters
    encounters = [
        ("sandstorm", "🌪️  A massive sandstorm approaches rapidly!"),
        ("desert_bandits", "🏴‍☠️ Desert bandits emerge from behind sand dunes!"),
        ("ancient_pyramid", "🔺 You discover an ancient pyramid half-buried in sand.")
    ]

    encounter_type, description = random.choice(encounters)
    slow_type(description)

    if encounter_type == "sandstorm":
        sandstorm_encounter()
    elif encounter_type == "desert_bandits":
        bandit_encounter()
    elif encounter_type == "ancient_pyramid":
        pyramid_encounter()

    continue_adventure()

def continue_adventure():
    """Continue the adventure or end the game."""
    slow_type(f"\n🎯 Current Status:")
    player.show_stats()

    if player.level >= 5:
        slow_type("\n🏆 Congratulations! You have become a legendary adventurer!")
        slow_type("Your tales will be told for generations to come!")
        victory_ending()
    else:
        choice = get_choice("What would you like to do? (continue/rest/quit)",
                           ["continue", "rest", "quit"])

        if choice == "continue":
            main_crossroads()
        elif choice == "rest":
            slow_type("😴 You rest and recover your strength...")
            player.heal(30)
            main_crossroads()
        else:
            slow_type("👋 Thanks for playing! Your adventure ends here.")
            sys.exit()

def victory_ending():
    """Victory ending sequence."""
    global player
    slow_type("🎊 VICTORY! You have completed your epic adventure!")
    slow_type(f"🏅 Final Stats for {player.name}:")
    player.show_stats()

    choice = get_choice("Would you like to start a new adventure? (yes/no)",
                       ["yes", "no"])

    if choice == "yes":
        # Reset player for new game
        game_intro()
    else:
        slow_type("🌟 Thank you for playing the Enhanced Adventure Game!")
        sys.exit()

def game_over():
    """Game over sequence."""
    global player
    slow_type("💀 GAME OVER")
    slow_type(f"⚰️  {player.name} has fallen in battle...")
    slow_type(f"📊 Final Stats:")
    player.show_stats()

    choice = get_choice("Would you like to try again? (yes/no)",
                       ["yes", "no"])

    if choice == "yes":
        game_intro()
    else:
        slow_type("👋 Thanks for playing!")
        sys.exit()

# Additional encounter functions
def oasis_encounter():
    """Desert oasis encounter."""
    slow_type("🌴 You reach the oasis and drink deeply from the cool water.")
    player.heal(40)
    player.add_item("Water Flask")
    slow_type("💧 You fill your water flask for the journey ahead.")

def sandstorm_encounter():
    """Sandstorm survival challenge."""
    slow_type("🌪️  The sandstorm engulfs you!")

    if player.has_item("Desert Cloak") or player.has_item("Shelter"):
        slow_type("🏠 You take shelter and wait out the storm safely.")
    else:
        choice = get_choice("How do you survive? (dig/run/endure)",
                           ["dig", "run", "endure"])

        if choice == "dig":
            slow_type("⛏️  You dig a shelter in the sand and survive!")
            player.gain_experience(20)
        elif choice == "run":
            if random.random() < 0.5:
                slow_type("🏃 You outrun the storm!")
            else:
                slow_type("💨 The storm catches you!")
                player.take_damage(25)
        else:
            slow_type("💪 You endure the storm through sheer willpower!")
            player.take_damage(15)
            player.gain_experience(25)

def bandit_encounter():
    """Desert bandit encounter."""
    bandit = Enemy("Desert Bandit", 50, 14, 3, 35, 25)
    slow_type("🏴‍☠️ The bandit leader steps forward menacingly!")

    choice = get_choice("What do you do? (fight/negotiate/intimidate)",
                       ["fight", "negotiate", "intimidate"])

    if choice == "fight":
        if combat(player, bandit):
            slow_type("🎯 You defeat the bandit and claim their treasure!")
            player.add_item("Bandit's Map")
    elif choice == "negotiate":
        if player.gold >= 40:
            player.gold -= 40
            slow_type("💰 You pay the bandits 40 gold and they let you pass.")
        else:
            slow_type("💸 You don't have enough gold! They attack!")
            combat(player, bandit)
    elif choice == "intimidate":
        if player.level >= 3 or player.has_item("Dragon Scale Armor"):
            slow_type("😨 The bandits are intimidated by your reputation and flee!")
            player.gain_experience(30)
        else:
            slow_type("😤 The bandits laugh at your attempt to intimidate them!")
            combat(player, bandit)

def pyramid_encounter():
    """Ancient pyramid exploration."""
    slow_type("🔺 You enter the mysterious pyramid...")
    slow_type("Ancient hieroglyphs cover the walls, and treasure glints in the darkness.")

    choice = get_choice("What do you explore? (tomb/treasure/hieroglyphs)",
                       ["tomb", "treasure", "hieroglyphs"])

    if choice == "tomb":
        slow_type("⚱️  You discover an ancient pharaoh's tomb!")
        if random.random() < 0.6:
            slow_type("👻 The pharaoh's spirit is restless!")
            mummy = Enemy("Ancient Mummy", 70, 16, 5, 50, 60)
            if combat(player, mummy):
                player.add_item("Pharaoh's Crown")
                slow_type("👑 You claim the pharaoh's golden crown!")
        else:
            slow_type("💎 You find ancient treasures without disturbing the dead!")
            player.gold += random.randint(40, 80)

    elif choice == "treasure":
        slow_type("💰 You focus on gathering the visible treasures...")
        player.gold += random.randint(30, 60)
        if random.random() < 0.3:  # 30% chance of trap
            slow_type("🪤 You trigger an ancient trap!")
            player.take_damage(20)

    elif choice == "hieroglyphs":
        slow_type("📜 You study the ancient hieroglyphs carefully...")
        player.add_item("Ancient Scroll")
        player.gain_experience(40)
        slow_type("🧠 You learn ancient secrets and magical knowledge!")

# Main game execution
if __name__ == "__main__":
    try:
        game_intro()
    except KeyboardInterrupt:
        slow_type("\n\n👋 Game interrupted. Thanks for playing!")
        sys.exit()
    except Exception as e:
        slow_type(f"\n❌ An error occurred: {e}")
        slow_type("Please restart the game.")
        sys.exit()
